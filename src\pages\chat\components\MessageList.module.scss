/* MessageList 组件样式 */

.messageList {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: #fbfbfb;
}

.emptyState {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #666;
  padding: 20px;
  background-color: #fbfbfb;
}

.emptyStateIcon {
  background-color: #7b4ffe;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
  box-shadow: 0 8px 24px rgba(123, 79, 254, 0.2);
}

.emptyStateIconInner {
  background-color: #fff;
  border-radius: 8px;
  width: 60px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  color: #7b4ffe;
}

.emptyStateTitle {
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.emptyStateDescription {
  color: #666;
  text-align: center;
  max-width: 400px;
  line-height: 1.6;
}

.emptyStatePrompt {
  color: #999;
  text-align: center;
  margin-bottom: 24px;
}

/* 消息项样式 */
.messageItem {
  margin-bottom: 24px;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }
}

.userMessage {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.assistantMessage {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
}

.messageAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
  flex-shrink: 0;
}

.userAvatar {
  background-color: #7b4ffe;
  color: white;
}

.assistantAvatar {
  background-color: #f0f0f0;
  color: #666;
}

.messageBubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
}

.userBubble {
  background-color: #f2f3f7;
  color: white;
  border-bottom-right-radius: 4px;
  border: 1px solid #e9eef4;
  box-shadow: 0 2px 8px rgba(158, 158, 158, 0.2);
}

.assistantBubble {
  background: #e8eafc;

  color: #333;
  border: 1px solid #e8e8e8;
  border-bottom-left-radius: 4px;
  box-shadow: 0px 0px 8px 0px #7090b033;
}

.streaming {
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

.messageContent {
  width: 100%;

  .markdownContent {
    width: 100%;
    margin: 0;

    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin: 0;
      }
    }

    pre {
      width: 100%;
      max-width: 100%;
      overflow-x: auto;
      margin: 8px 0;
      background-color: rgba(0, 0, 0, 0.05) !important;
    }

    code {
      background-color: rgba(0, 0, 0, 0.1) !important;
    }

    table {
      width: 100%;
      max-width: 100%;
    }
  }
}

.messageActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  width: fit-content; // 确保按钮容器不会占用过多空间
}

.messageItem:hover .messageActions {
  opacity: 1;
}

.messageTime {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .messageBubble {
    max-width: 85%;
  }

  .messageList {
    padding: 12px;
  }
}

@media (min-width: 769px) {
  .messageBubble {
    max-width: 90%;
    min-width: 50px;
  }
}

@media (min-width: 1200px) {
  .messageBubble {
    max-width: 85%;
    min-width: 50px;
  }
}

{"name": "react-admin-antd", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "test": "vite --mode test", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:prod": "vite preview --mode production"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/plots": "^2.3.2", "@antv/g-svg": "^2.0.41", "@antv/g6": "^5.0.49", "@dnd-kit/core": "^6.2.0", "@dnd-kit/sortable": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortaine/fetch-event-source": "^3.0.6", "@monaco-editor/react": "^4.6.0", "@reactflow/core": "^11.11.4", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/quill": "^2.0.14", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ahooks": "^3.8.1", "animate.css": "^4.1.1", "antd": "^5.21.5", "antd-style": "^3.7.1", "axios": "^1.7.7", "classnames": "^2.5.1", "dayjs": "^1.11.13", "emoji-picker-react": "^4.13.2", "framer-motion": "^11.11.15", "fuse.js": "^7.1.0", "heic2any": "^0.0.4", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "i18next": "^23.16.5", "idb-keyval": "^6.2.2", "katex": "^0.16.22", "lodash-es": "^4.17.21", "markdown-to-txt": "^2.0.1", "mermaid": "^11.9.0", "mobx": "^6.13.5", "mobx-react-lite": "^4.0.7", "mockjs": "^1.1.0", "mqtt": "^5.10.3", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "path": "^0.12.7", "quill": "^2.0.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-image-crop": "^11.0.7", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-masonry-css": "^1.0.16", "react-particles": "^2.12.2", "react-router-dom": "^6.27.0", "react-split": "^2.0.14", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "react-window": "^1.8.11", "reactflow": "^11.11.4", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "spark-md5": "^3.0.2", "swiper": "^11.1.15", "tsparticles": "^3.5.0", "tsparticles-slim": "^2.12.0", "use-debounce": "^10.0.5", "zod": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.11.1", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.9.0", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/react-transition-group": "^4.4.11", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "msw": "^2.6.6", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.80.6", "tailwindcss": "^3.4.14", "terser": "^5.36.0", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-pwa": "^0.21.1"}}
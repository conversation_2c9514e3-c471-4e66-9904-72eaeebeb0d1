import React, { useState, useEffect, useRef } from "react";
import { Col, Row, Timeline } from "antd";
import securityEventsData from "../mockData/mock";
import styles from "./index.module.scss";
import AttackGraph from "../components/attack";

interface SecurityEvent {
  id: number;
  timestamp: string;
  ip: string;
  content: string;
  nodes: Array<{
    id: string;
    data: { label: string };
    style: any;
    type?: string;
  }>;
  edges: Array<{
    source: string;
    target: string;
    data: { label: string };
    style: any;
  }>;
}

interface GraphData {
  nodes: Array<{
    id: string;
    data: { label: string };
    style: any;
    type?: string;
  }>;
  edges: Array<{
    source: string;
    target: string;
    data: { label: string };
    style: any;
  }>;
}

const BasicTable: React.FC = () => {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [graphData, setGraphData] = useState<GraphData>({
    nodes: [],
    edges: [],
  });
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const currentIndexRef = useRef(0);

  // 格式化时间标签为ReactNode结构
  const formatTimeLabel = (dateTimeStr: string) => {
    const date = new Date(dateTimeStr);
    const dateStr = date.toLocaleDateString("zh-CN");
    const timeStr = date.toLocaleTimeString("zh-CN");

    return (
      <div style={{ textAlign: "center" }}>
        <div style={{ fontSize: "12px", color: "#666" }}>{dateStr}</div>
        <div style={{ fontSize: "14px", fontWeight: "bold" }}>{timeStr}</div>
      </div>
    );
  };

  // 格式化children为ReactNode结构
  const formatChildren = (ip: string, content: string) => {
    return (
      <div>
        <div
          style={{ fontSize: "12px", color: "#1890ff", marginBottom: "4px" }}
        >
          {ip}
        </div>
        <div style={{ fontSize: "13px", lineHeight: "1.4" }}>{content}</div>
      </div>
    );
  };

  // 处理安全事件数据
  const processSecurityEvents = (events: SecurityEvent[]) => {
    return events.map((event) => ({
      ...event,
      label: formatTimeLabel(event.timestamp),
      children: formatChildren(event.ip, event.content),
    }));
  };

  // 更新图谱数据
  const updateGraphData = (newEvent: SecurityEvent) => {
    setGraphData((prevGraphData) => {
      const existingNodeIds = new Set(
        prevGraphData.nodes.map((node) => node.id)
      );
      const existingEdgeIds = new Set(
        prevGraphData.edges.map((edge) => `${edge.source}-${edge.target}`)
      );

      // 过滤出新的节点（避免重复）
      const newNodes = newEvent.nodes.filter(
        (node) => !existingNodeIds.has(node.id)
      );

      // 过滤出新的边（避免重复）
      const newEdges = newEvent.edges.filter(
        (edge) => !existingEdgeIds.has(`${edge.source}-${edge.target}`)
      );

      return {
        nodes: [...prevGraphData.nodes, ...newNodes],
        edges: [...prevGraphData.edges, ...newEdges],
      };
    });
  };

  // 定时器推送数据
  const startDataPush = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      if (currentIndexRef.current < securityEventsData.length) {
        const newEvent = securityEventsData[currentIndexRef.current];
        setSecurityEvents((prev) => [...prev, newEvent]);
        updateGraphData(newEvent); // 同时更新图谱数据
        currentIndexRef.current += 1;
      } else {
        // 所有数据推送完毕，停止定时器
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      }
    }, Math.random() * 3000 + 1000); // 1-4秒随机间隔
  };

  // 组件挂载时启动数据推送
  useEffect(() => {
    startDataPush();

    // 清理定时器
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // 处理后的时间线数据
  const timelineItems = processSecurityEvents(securityEvents);

  return (
    <Row className={styles.timelineContainer}>
      <Col span={6}>
        <Timeline mode={"left"} items={timelineItems} reverse />
      </Col>
      <Col span={18}>
        <AttackGraph graphData={graphData}></AttackGraph>
      </Col>
    </Row>
  );
};

export default BasicTable;

export const routeConfig = {
  title: "route.superagent.graph",
  sort: 5,
};

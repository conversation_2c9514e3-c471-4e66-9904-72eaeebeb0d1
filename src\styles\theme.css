/* 基础主题变量 */
:root {
  /* 背景色系统 */
  --bg-primary: #ffffff;
  --bg-primary-rgb: 255, 255, 255;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f3f4f6;
  --bg-overlay: rgba(0, 0, 0, 0.05);
  --bg-hover: rgba(0, 0, 0, 0.04);
  --bg-active: rgba(0, 0, 0, 0.08);
 --layout-bg:  radial-gradient(circle at top left, #f0f9ff, transparent),
  radial-gradient(circle at bottom right, #bfdbfe, transparent),
  linear-gradient(135deg, #e0f2fe, #dbeafe);/* 文字颜色系统 */
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --text-disabled: #9ca3af;
  --text-inverse: #ffffff;
  
  /* 边框颜色系统 */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-divider: #f3f4f6;
  
  /* 菜单颜色系统 */
  --menu-bg: transparent;
  --menu-text: var(--text-secondary);
  --menu-hover-bg: transparent;
  --menu-hover-text: var(--primary-color);
  --menu-active-bg: transparent;
  --menu-active-text: var(--primary-color);
  
  /* 状态颜色 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  /* 功能色 */
  --link: #1890ff;
  --link-hover: #40a9ff;
  
  /* 投影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  /* 圆角 */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;
  
  /* Antd 组件主题变量 */
  --menu-item-color: var(--text-primary);
  --menu-item-hover-color: var(--primary-color);
  --menu-item-active-color: var(--primary-color);
  --menu-item-selected-color: var(--primary-color);
  --menu-border-color: transparent;
  --menu-bg-color: transparent;
  
  --dropdown-bg: var(--bg-primary);
  --dropdown-item-color: var(--text-primary);
  --dropdown-item-hover-bg: var(--bg-hover);
  --dropdown-item-hover-color: var(--primary-color);
  --dropdown-border-color: var(--border-primary);
  
  --button-border-color: var(--border-primary);
  --button-text-color: var(--text-primary);
  
  --input-bg: var(--bg-primary);
  --input-border-color: var(--border-primary);
  --input-text-color: var(--text-primary);
  --input-placeholder-color: var(--text-disabled);
  
  /* 布局变量 */

  /* Tab 样式变量 */
  --tab-active-color: var(--primary-color);
  --tab-hover-bg: var(--bg-hover);
  --tab-active-bg: var(--bg-active);
  --tab-border-color: transparent;
  
  --primary-color: var(--ant-color-primary);
}

/* 风格相关的样式类 */
.theme-style {
  position: relative;
  box-sizing: border-box;
  background: rgba(var(--bg-primary-rgb), var(--bg-opacity));
  backdrop-filter: blur(var(--blur-amount));
  border-radius: var(--style-radius);
  box-shadow: var(--style-shadow);
  border: var(--style-border);
  margin: var(--style-margin);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 灵动风格（类似 macOS） */
:root.theme-dynamic {
  
  /* 背景效果 */
  --bg-opacity: 0.6;
  --blur-amount: 4px;

  /* 边框效果 */
  /* --style-border: 1px solid rgba(255, 255, 255, 0.2); */
  --style-border: unset;
  --style-radius: 12px;
  
  /* 阴影效果 */
  --style-shadow: 
    0 2px 10px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  
  /* 间距效果 */
  --style-margin: 10px;
  
  /* 内容区域效果 */
  --content-spacing: 1rem;
  --inner-radius: 8px;
  /* Header部分*/
  --header-height: calc(var(--header-nav-height) + var(--header-tags-height) + var(--header-border-height) + var(--header-margin-height));
  --header-nav-height: 56px;
  --header-tags-height: 0px;
  --header-border-height: 2px;
  --header-margin-height:calc(2 *   var(--style-margin));  

  /* 卡片效果 */
  --card-bg-opacity: 0.7;
  --card-blur: 10px;
  --card-border: 1px solid rgba(255, 255, 255, 0.1);
  --card-radius: 8px;
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  --card-margin: 0.5rem;
}

/* 经典风格（传统后台） */
:root.theme-classic {
  /* 背景效果 */
  --bg-opacity: 1;
  --blur-amount: 0;
  --layout-bg:  white;
  /* 边框效果 */
  /* --style-border: 1px solid var(--border-primary); */
  --style-radius: 2px;
  
  /* 阴影效果 */
  --style-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  
  /* 间距效果 */
  --style-margin: 0px;
    /* Header部分*/
    --header-height: calc(var(--header-nav-height) + var(--header-tags-height) + var(--header-border-height) + var(--header-margin-height));
    --header-nav-height: 56px;
    --header-tags-height: 0px;
    --header-border-height: 2px;
    --header-margin-height:calc(2 *   var(--style-margin));  
  /* 内容区域效果 */
  --content-spacing: 1rem;
  --inner-radius: 2px;
  
  /* 卡片效果 */
  --card-bg-opacity: 1;
  --card-blur: 0;
  --card-border: 1px solid var(--border-primary);
  --card-radius: 2px;
  --card-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  --card-margin: 0;
}

/* 暗色模式下的变量覆盖 */
:root.dark {
  /* 背景色系统 */
  --bg-primary: #1f2937;
  --bg-primary-rgb: 31, 41, 55;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --bg-overlay: rgba(255, 255, 255, 0.1);
  --bg-hover: rgba(255, 255, 255, 0.08);
  --bg-active: rgba(255, 255, 255, 0.12);
  --layout-bg:      radial-gradient(circle at 100% 100%, #334155 0%, transparent 50%),
  radial-gradient(circle at 50% 50%, #1e293b 0%, #0f172a 100%),
  linear-gradient(135deg, #334155 0%, #1e293b 50%, #0f172a 100%);
  /* 文字颜色系统 - 调整为更柔和的色调 */
  --text-primary: rgba(255, 255, 255, 0.85);    /* 主要文字，不那么刺眼的白色 */
  --text-secondary: rgba(255, 255, 255, 0.65);  /* 次要文字，更柔和 */
  --text-tertiary: rgba(255, 255, 255, 0.45);   /* 第三级文字 */
  --text-disabled: rgba(255, 255, 255, 0.35);   /* 禁用状态文字 */
  --text-inverse: #1f2937;                      /* 反色文字保持不变 */
  
  /* 边框颜色系统 */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --border-divider: #374151;
  
  /* 菜单颜色系统 */
  --menu-bg: transparent;
  --menu-text: var(--text-secondary);
  --menu-hover-bg: transparent;
  --menu-hover-text: var(--primary-color);
  --menu-active-bg: transparent;
  --menu-active-text: var(--primary-color);
  
  /* 投影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
  
  /* Antd 组件暗色主题变量 */
  --menu-item-color: var(--text-primary);
  --menu-item-hover-color: var(--primary-color);
  --menu-item-active-color: var(--primary-color);
  --menu-item-selected-color: var(--primary-color);
  
  --dropdown-bg: var(--bg-primary);
  --dropdown-item-color: var(--text-primary);
  --dropdown-item-hover-bg: var(--bg-hover);
  --dropdown-border-color: var(--border-primary);

  &.theme-dynamic {
    /* 背景效果 */
    --bg-opacity: 0.75;
    
    /* 边框效果 */
    /* --style-border: 1px solid rgba(255, 255, 255, 0.1); */
    
    /* 阴影效果 */
    --style-shadow: 
      0 2px 10px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.05);
    
    /* 卡片效果 */
    --card-bg-opacity: 0.6;
    --card-border: 1px solid rgba(255, 255, 255, 0.05);
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.theme-classic {
    /* 阴影效果 */
    --style-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    
    /* 卡片效果 */
    --card-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

/* 卡片样式类 */
.card-style {
  background: rgba(var(--bg-primary-rgb), var(--card-bg-opacity));
  backdrop-filter: blur(var(--card-blur));
  border: var(--card-border);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  margin: var(--card-margin);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 灵动风格特定变量 */
.theme-dynamic {
  --bg-opacity: 0.8;
  --blur-amount: 10px;
  --border-opacity: 0.2;
}

/* 经典风格特定变量 */
.theme-classic {
  --bg-opacity: 1;
  --blur-amount: 0px;
  --border-opacity: 1;
}

/* 滚动条基础样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 6px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  /* background: var(--ant-primary-3); */
  border-radius: 6px;
  border: 1px solid #f0f2f5;
  background-clip: padding-box;
  min-height: 30px;
  opacity: 0.8;  /* 添加透明度 */
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ant-primary-4);
  border-width: 2px;
}

::-webkit-scrollbar-thumb:active {
  background: var(--ant-primary-5);
  border-width: 1px;
}

/* 暗色主题滚动条样式优化 */
.dark ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);  /* 更微妙的轨道背景 */
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--ant-primary-3);  /* 使用较浅的主题色 */
  border: 3px solid rgba(255, 255, 255, 0.05);  /* 边框颜色配合轨道 */
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--ant-primary-2);  /* 悬停时更亮 */
  border-width: 2px;
  opacity: 1;
}

.dark ::-webkit-scrollbar-thumb:active {
  background: var(--ant-primary-1);  /* 点击时最亮 */
  border-width: 1px;
}

/* 暗色主题下特定区域的滚动条 */
.dark .ant-layout-content::-webkit-scrollbar-thumb,
.dark .ant-table-body::-webkit-scrollbar-thumb,
.dark .ant-modal-body::-webkit-scrollbar-thumb,
.dark .ant-drawer-body::-webkit-scrollbar-thumb {
  background: var(--ant-primary-3);
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

/* ... 其他主题相关样式 ... */ 
.bg-gradient-animated {
  animation: gradientShift 15s ease infinite;
}
@keyframes gradientShift {
  0% { background-position: 0% 50% }
  50% { background-position: 100% 50% }
  100% { background-position: 0% 50% }
} 
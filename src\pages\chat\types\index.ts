// 基础类型定义
export type ModelType = string;

export interface MultimodalContent {
  type: "text" | "image_url";
  text?: string;
  image_url?: {
    url: string;
  };
}

export interface RequestMessage {
  role: "system" | "user" | "assistant";
  content: string | MultimodalContent[];
}

export interface ChatMessageTool {
  id: string;
  index?: number;
  type?: string;
  function?: {
    name: string;
    arguments?: string;
  };
  content?: string;
  isError?: boolean;
  errorMsg?: string;
}

export interface ChatMessage extends RequestMessage {
  date: string;
  streaming?: boolean;
  isError?: boolean;
  id: string;
  model?: ModelType;
  tools?: ChatMessageTool[];
  audio_url?: string;
}

export interface ChatStat {
  tokenCount: number;
  wordCount: number;
  charCount: number;
}

// 智能体接口定义
export interface Agent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  category: string;
  usageCount: number;
  systemPrompt?: string;
}

export interface ChatSession {
  id: string;
  topic: string;
  memoryPrompt: string;
  messages: ChatMessage[];
  stat: ChatStat;
  lastUpdate: number;
  lastSummarizeIndex: number;
  clearContextIndex?: number;
  mask: Mask;
  isLoading?: boolean; // 添加会话级别的加载状态
  agentId?: string; // 关联的智能体ID
}

export interface Mask {
  id: string;
  createdAt: number;
  avatar: string;
  name: string;
  hideContext?: boolean;
  context: ChatMessage[];
  syncGlobalConfig?: boolean;
  modelConfig: ModelConfig;
  lang: string;
  builtin: boolean;
  plugin?: string[];
}

export interface ModelConfig {
  model: ModelType;
  providerName: string;
  temperature: number;
  top_p: number;
  max_tokens: number;
  presence_penalty: number;
  frequency_penalty: number;
  sendMemory: boolean;
  historyMessageCount: number;
  compressMessageLengthThreshold: number;
  compressModel: string;
  compressProviderName: string;
  enableInjectSystemPrompts: boolean;
  template: string;
  size: string;
  quality: string;
  style: string;
}

export interface TTSConfig {
  enable: boolean;
  autoplay: boolean;
  engine: string;
  model: string;
  voice: string;
  speed: number;
}

export interface ChatConfig {
  historyMessageCount: number;
  compressMessageLengthThreshold: number;
  sendBotMessages: boolean;
  submitKey: string;
  avatar: string;
  fontSize: number;
  theme: string;
  tightBorder: boolean;
  sendPreviewBubble: boolean;
  enableAutoGenerateTitle: boolean;
  sidebarWidth: number;
  disablePromptHint: boolean;
  dontShowMaskSplashScreen: boolean;
  hideBuiltinMasks: boolean;
  customModels: string;
  defaultModel: string;
}

export interface LLMUsage {
  used: number;
  total: number;
}

export interface LLMModel {
  name: string;
  displayName?: string;
  available: boolean;
  provider: LLMModelProvider;
}

export interface LLMModelProvider {
  id: string;
  providerName: string;
  providerType: string;
  sorted?: number;
}

export interface ChatControllerPool {
  controller?: AbortController;
  sessionIndex: number;
  messageIndex: number;
}

export interface PromptHint {
  id: string;
  title: string;
  content: string;
}

export interface RenderPompt {
  title: string;
  content: string;
}

export interface Prompt {
  id: string;
  isUser?: boolean;
  title: string;
  content: string;
  createdAt: number;
}

export interface SearchChatSession {
  sessionId: string;
  session: ChatSession;
  messageId: string;
  message: ChatMessage;
}

// 导出类型
export type ServiceProvider = 
  | "OpenAI"
  | "Azure" 
  | "Anthropic"
  | "Google"
  | "Baidu"
  | "ByteDance"
  | "Alibaba"
  | "Tencent"
  | "Moonshot"
  | "Iflytek"
  | "XAI"
  | "ChatGLM"
  | "DeepSeek";

.split-container {
  display: flex;
  height: 600px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  &.horizontal {
    flex-direction: row;
  }

  &.vertical {
    flex-direction: column;
  }

  .split-container-inner {
    display: flex;
    height: 100%;
    
    &.horizontal {
      flex-direction: row;
    }

    &.vertical {
      flex-direction: column;
    }
  }
}

.panel {
  height: 100%;
  width: 100%;
  padding: 16px;
  overflow: auto;
  transition: background-color 0.3s;

  .panel-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
  }
}

.gutter {
  background-color: #f5f5f5;
  position: relative;

  &:hover {
    background-color: #e8e8e8;
  }

  &.gutter-horizontal {
    cursor: col-resize;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 4px;
      height: 24px;
      background-color: #1890ff;
      opacity: 0;
      transition: opacity 0.2s;
      border-radius: 2px;
    }
  }

  &.gutter-vertical {
    cursor: row-resize;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 24px;
      height: 4px;
      background-color: #1890ff;
      opacity: 0;
      transition: opacity 0.2s;
      border-radius: 2px;
    }
  }

  &:hover::after {
    opacity: 1;
  }
}

// 暗色主题适配
.dark {
  .split-container {
    border-color: #303030;
    background-color: #141414;
  }

  .panel {
    .panel-title {
      color: #fff;
    }
  }

  .gutter {
    background-color: #1f1f1f;

    &:hover {
      background-color: #2f2f2f;
    }
  }
} 
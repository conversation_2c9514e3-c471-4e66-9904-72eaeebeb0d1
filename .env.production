# 生产环境配置
VITE_NODE_ENV=production

# 项目基础配置
VITE_APP_TITLE=React Admin
VITE_APP_DESC=React Admin 生产环境

# 服务配置
VITE_PORT=80
VITE_PROXY=false

# API配置
VITE_API_URL=https://api.example.com
VITE_API_PREFIX=/api
VITE_UPLOAD_URL=https://api.example.com/upload
VITE_WS_URL=wss://api.example.com/ws

# 功能配置
VITE_USE_MOCK=false
VITE_USE_PWA=true
VITE_USE_HTTPS=true

# 调试配置
VITE_DEV_TOOLS=false
VITE_DROP_CONSOLE=true
VITE_DROP_DEBUGGER=true

# CDN配置
VITE_USE_CDN=true
VITE_CDN_URL=https://cdn.example.com 
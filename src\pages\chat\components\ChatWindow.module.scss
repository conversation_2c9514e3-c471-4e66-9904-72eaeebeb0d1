/* ChatWindow 组件样式 */

.chatWindow {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  backdrop-filter: blur(8px);
}

.headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 8px;
}

.moreButton {
  font-size: 16px;
}

.content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 64px);
  overflow: hidden;
  position: relative;
}

.centeredContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }

  .headerTitle {
    font-size: 14px;
  }

  .centeredContent {
    padding: 20px 16px;
  }
}

// 节点类型定义
type NodeType = "c2" | "domain" | "file" | "process" | "server" | "exploit";

// 节点样式配置函数
const getNodeStyle = (type: NodeType) => {
  const styles = {
    c2: {
      fill: "#F44336",
      stroke: "#F44336",
      size: 40,
      labelText: "C&C Server",
      labelFill: "#ffffffff",
      iconFontFamily: "iconfont",
      iconText: String.fromCharCode(0xe622),
    },
    domain: { fill: "#4CAF50", stroke: "#4CAF50", size: 30 },
    file: { fill: "#2196F3", stroke: "#2196F3", size: 40 },
    process: { fill: "#FFEB3B", stroke: "#FFEB3B", size: 30 },
    server: { fill: "#9C27B0", stroke: "#9C27B0", size: 35 },
    exploit: { fill: "#795548", stroke: "#795548", size: 40 },
  };
  return styles[type] || styles.server;
};

// 根据内容判断节点类型
const getNodeType = (content: string, nodeId: string): NodeType => {
  if (content.includes("C&C") || nodeId.includes("************")) return "c2";
  if (content.includes("下载") && content.includes(".tgz")) return "file";
  if (content.includes("exploit") || content.includes("漏洞")) return "exploit";
  if (content.includes("进程") || content.includes("daemon")) return "process";
  if (content.includes(".com") || content.includes("域名")) return "domain";
  return "server";
};

// 创建节点
const createNode = (nodeId: string, content: string) => {
  const nodeType = getNodeType(content, nodeId);
  return {
    id: nodeId,
    data: { label: nodeId },
    style: getNodeStyle(nodeType),
    type: nodeType === "c2" ? "diamond" : undefined,
  };
};

// 创建边
const createEdge = (source: string, target: string, content: string) => {
  const isImportant =
    content.includes("C&C") ||
    content.includes("数据外传") ||
    content.includes("横向移动");
  return {
    source,
    target,
    data: { label: content.split("：")[0] || "连接" },
    style: {
      markerSize: 14, // 统一使用14
      markerColor: isImportant ? "#ff4757" : "#2ed573",
    },
  };
};

const securityEvents = [
  {
    id: 1,
    timestamp: "2025-04-18 19:58:00",
    ip: "**************",
    content: "内网横向移动：通过frpc连接到************:22（SSH服务）",
    nodes: [
      createNode("**************", "内网横向移动"),
      createNode("************", "SSH服务"),
    ],
    edges: [createEdge("**************", "************", "内网横向移动")],
  },
  {
    id: 2,
    timestamp: "2025-04-18 20:03:00",
    ip: "************",
    content: "用户test通过弱密码登录成功，使用sshd服务，源IP为**************",
    nodes: [createNode("************", "弱密码登录")],
    edges: [createEdge("**************", "************", "弱密码登录")],
  },
  {
    id: 3,
    timestamp: "2025-04-18 20:15:00",
    ip: "************",
    content: "test账号通过wget下载可疑漏洞利用包（PwnKit-Exploit-main.tgz）",
    nodes: [createNode("PwnKit-Exploit-main.tgz", "下载可疑漏洞利用包")],
    edges: [
      createEdge("************", "PwnKit-Exploit-main.tgz", "下载漏洞利用包"),
    ],
  },
  {
    id: 4,
    timestamp: "2025-04-18 20:15:00",
    ip: "************",
    content:
      "解压编译exploit.c -Wall exploit.c -o exploit并执行exploit，疑似提权成功",
    nodes: [createNode("exploit", "解压编译exploit")],
    edges: [createEdge("PwnKit-Exploit-main.tgz", "exploit", "编译执行")],
  },
  {
    id: 5,
    timestamp: "2025-04-18 20:17:00",
    ip: "************",
    content: "权限维持：向/root/.ssh/authorized_keys写入内容",
    nodes: [createNode("authorized_keys", "权限维持")],
    edges: [createEdge("************", "authorized_keys", "权限维持")],
  },
  {
    id: 6,
    timestamp: "2025-04-18 20:28:00",
    ip: "************",
    content: "来自************的成功SSH公钥登录",
    nodes: [],
    edges: [createEdge("************", "************", "SSH公钥登录")],
  },
  {
    id: 7,
    timestamp: "2025-04-18 20:35:00",
    ip: "************",
    content: "成功SSH公钥登录（root用户）",
    nodes: [],
    edges: [createEdge("************", "************", "root登录")],
  },
  {
    id: 8,
    timestamp: "2025-04-18 20:42:00",
    ip: "************",
    content: "异常文件读取：尝试访问/etc/shadow",
    nodes: [createNode("/etc/shadow", "异常文件读取")],
    edges: [createEdge("************", "/etc/shadow", "异常文件读取")],
  },
  {
    id: 9,
    timestamp: "2025-04-18 20:45:00",
    ip: "************",
    content: "数据库访问：连接到************:3306（MySQL服务）",
    nodes: [createNode("************", "MySQL服务")],
    edges: [createEdge("************", "************", "数据库访问")],
  },
  {
    id: 10,
    timestamp: "2025-04-18 20:50:00",
    ip: "************",
    content: "敏感数据导出：/var/lib/mysql/目录下的data_dump.sql被下载",
    nodes: [createNode("data_dump.sql", "敏感数据导出")],
    edges: [createEdge("************", "data_dump.sql", "敏感数据导出")],
  },
  {
    id: 11,
    timestamp: "2025-04-18 20:55:00",
    ip: "************",
    content: "尝试横向移动：ping ************",
    nodes: [createNode("************", "尝试横向移动")],
    edges: [createEdge("************", "************", "横向移动")],
  },
  {
    id: 12,
    timestamp: "2025-04-18 21:00:00",
    ip: "************",
    content: "网络扫描：使用nmap工具扫描***********/24网段",
    nodes: [createNode("***********/24", "网络扫描")],
    edges: [createEdge("************", "***********/24", "网络扫描")],
  },
  {
    id: 13,
    timestamp: "2025-04-18 21:10:00",
    ip: "************",
    content: "发现可疑进程：/tmp/.systemd.daemon（可能为后门程序）",
    nodes: [createNode("/tmp/.systemd.daemon", "发现可疑进程")],
    edges: [createEdge("************", "/tmp/.systemd.daemon", "可疑进程")],
  },
  {
    id: 14,
    timestamp: "2025-04-18 21:15:00",
    ip: "************",
    content: "数据外传：通过nc命令将文件传输到公网IP ************:5555",
    nodes: [createNode("************", "数据外传")],
    edges: [createEdge("************", "************", "数据外传")],
  },
  {
    id: 15,
    timestamp: "2025-04-18 21:20:00",
    ip: "*************",
    content: "异常流量检测：与C&C服务器************:8080建立TCP连接",
    nodes: [createNode("*************", "异常流量检测")],
    edges: [createEdge("*************", "************", "C&C通信")],
  },
];

// 按时间排序（确保按发生顺序）
securityEvents.sort(
  (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
);

// 导出数据（如果是模块环境）
export default securityEvents;

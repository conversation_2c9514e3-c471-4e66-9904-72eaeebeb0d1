.agentSelector {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: #fafafa;

  .header {
    margin-bottom: 24px;
    text-align: center;

    h3 {
      color: #262626;
      margin-bottom: 8px;
    }
  }

  .searchSection {
    margin-bottom: 16px;
  }

  .categoryTabs {
    .ant-tabs-content-holder {
      padding-top: 16px;
    }
  }

  .agentGrid {
    min-height: 400px;
  }

  .agentCard {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .ant-card-meta {
      .ant-card-meta-detail {
        .ant-card-meta-title {
          margin-bottom: 8px;
        }
      }
    }

    .ant-card-actions {
      border-top: 1px solid #f0f0f0;
      background: #fafafa;

      li {
        margin: 8px 0;

        .ant-btn {
          border-radius: 4px;
        }
      }
    }
  }

  .agentTitle {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .category {
      font-size: 12px;
      color: #8c8c8c;
      background: #f0f0f0;
      padding: 2px 6px;
      border-radius: 4px;
      width: fit-content;
    }
  }

  .agentDescription {
    .usageCount {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;
      text-align: right;
    }
  }

  .emptyState {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #8c8c8c;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .agentSelector {
    padding: 16px;

    .agentGrid {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}

// 深色主题支持
[data-theme="dark"] {
  .agentSelector {
    background: #141414;

    .agentCard {
      background: #1f1f1f;
      border-color: #303030;

      &:hover {
        border-color: #1890ff;
      }

      .ant-card-actions {
        background: #262626;
        border-top-color: #303030;
      }
    }

    .agentTitle .category {
      background: #262626;
      color: #bfbfbf;
    }

    .agentDescription .usageCount {
      border-top-color: #303030;
    }
  }
}

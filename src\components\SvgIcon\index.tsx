import React from 'react';

interface SvgIconProps extends React.SVGProps<SVGSVGElement> {
  name: string;
  size?: number | string;
}

// SVG图标映射
const iconMap: Record<string, string> = {
  chatgpt: '/src/icons/chatgpt.svg',
  mask: '/src/icons/mask.svg',
  mcp: '/src/icons/mcp.svg',
  discovery: '/src/icons/discovery.svg',
  settings: '/src/icons/settings.svg',
  github: '/src/icons/github.svg',
  delete: '/src/icons/delete.svg',
  add: '/src/icons/add.svg',
  drag: '/src/icons/drag.svg',
};

export const SvgIcon: React.FC<SvgIconProps> = ({ 
  name, 
  size = 24, 
  className = '',
  ...props 
}) => {
  const iconPath = iconMap[name];
  
  if (!iconPath) {
    console.warn(`SVG icon "${name}" not found`);
    return null;
  }

  return (
    <img
      src={iconPath}
      alt={name}
      width={size}
      height={size}
      className={`svg-icon ${className}`}
      {...props}
    />
  );
};

export default SvgIcon;

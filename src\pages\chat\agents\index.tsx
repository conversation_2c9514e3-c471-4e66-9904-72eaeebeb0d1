import React from 'react';
import { observer } from 'mobx-react-lite';
import { AgentSelector } from '../components/AgentSelector';
import { RouteConfig } from '@/types/route';
import { RobotOutlined } from '@ant-design/icons';

const AgentsPage: React.FC = observer(() => {
  return (
    <div style={{ height: '100%', overflow: 'hidden' }}>
      <AgentSelector />
    </div>
  );
});

export default AgentsPage;

export const routeConfig: RouteConfig = {
  title: "智能体选择",
  icon: <RobotOutlined />,
  layout: false,
  sort: 1,
};

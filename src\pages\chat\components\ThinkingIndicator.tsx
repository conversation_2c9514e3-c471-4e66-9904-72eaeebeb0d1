import React, { useState, useEffect, useRef } from "react";
import styles from "./ThinkingIndicator.module.scss";

interface ThinkingIndicatorProps {
  fontSize: number;
  sessionId?: string; // 添加会话ID以确保每个会话的独立性
}

export const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({
  fontSize,
  sessionId,
}) => {
  const [currentText, setCurrentText] = useState("");
  const [dotCount, setDotCount] = useState(0);
  const textIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const dotIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // 为每个组件实例生成唯一标识符，结合sessionId确保完全独立
  const instanceIdRef = useRef(
    `${sessionId || "default"}-${Date.now()}-${Math.random()}`
  );

  const thinkingTexts = [
    "正在思考",
    "分析问题中",
    "整理思路",
    "准备回复",
    "生成回答",
  ];

  // 清理定时器的通用函数
  const clearTimers = () => {
    if (textIntervalRef.current) {
      clearInterval(textIntervalRef.current);
      textIntervalRef.current = null;
    }
    if (dotIntervalRef.current) {
      clearInterval(dotIntervalRef.current);
      dotIntervalRef.current = null;
    }
  };

  // 管理定时器，确保每个会话实例独立
  useEffect(() => {
    // 重置mounted状态
    mountedRef.current = true;

    // 为新的会话生成新的实例ID
    instanceIdRef.current = `${
      sessionId || "default"
    }-${Date.now()}-${Math.random()}`;

    // 清除之前的定时器
    clearTimers();

    // 重置状态
    setDotCount(0);
    if (thinkingTexts.length > 0) {
      setCurrentText(thinkingTexts[0]);
    }

    // 创建新的定时器
    textIntervalRef.current = setInterval(() => {
      if (!mountedRef.current) return;

      const randomText =
        thinkingTexts[Math.floor(Math.random() * thinkingTexts.length)];
      setCurrentText(randomText);
    }, 2000);

    dotIntervalRef.current = setInterval(() => {
      if (!mountedRef.current) return;

      setDotCount((prev) => (prev + 1) % 4);
    }, 500);

    return () => {
      clearTimers();
    };
  }, [sessionId]); // 依赖sessionId，确保会话切换时重新初始化

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      clearTimers();
    };
  }, []);

  return (
    <div className={styles.thinkingContainer}>
      <div className={styles.contentWrapper}>
        <div className={styles.indicatorContent} style={{ fontSize: fontSize }}>
          <div className={styles.dotsContainer}>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
          </div>
          <div
            className={styles.textContent}
            style={{ fontSize: fontSize * 0.75 }}
          >
            {currentText}
            {"·".repeat(dotCount)}
          </div>
        </div>
      </div>
    </div>
  );
};

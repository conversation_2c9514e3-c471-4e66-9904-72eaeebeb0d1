.pulse-container {
  --pulse-color: var(--ant-primary-color, #1890ff);
  position: relative;
  width: 60px;
  height: 60px;

  .pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--pulse-color);
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse 1.5s ease-out infinite;

    &:nth-child(2) {
      animation-delay: 0.5s;
    }

    &:nth-child(3) {
      animation-delay: 1s;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(0);
      opacity: 0.6;
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }
}

[data-theme='dark'] .pulse-container {
  --pulse-color: var(--ant-primary-color, #177ddc);
} 
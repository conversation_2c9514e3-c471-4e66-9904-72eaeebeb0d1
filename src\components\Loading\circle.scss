.circle-container {
  --circle-color: var(--ant-primary-color, #1890ff);
  position: relative;
  width: 60px;
  height: 60px;

  .circle {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top-color: var(--circle-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;

    &:nth-child(2) {
      border-top-color: transparent;
      border-right-color: var(--circle-color);
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      border-top-color: transparent;
      border-right-color: transparent;
      border-bottom-color: var(--circle-color);
      animation-delay: 0.4s;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

[data-theme='dark'] .circle-container {
  --circle-color: var(--ant-primary-color, #177ddc);
} 
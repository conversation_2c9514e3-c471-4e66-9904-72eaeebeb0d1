.popularAgents {
  padding: 16px 20px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;

  .header {
    margin-bottom: 12px;
    text-align: center;

    .title {
      font-size: 13px;
      color: #8c8c8c;
    }
  }

  .agentGrid {
    .ant-col {
      display: flex;
    }
  }

  .agentCard {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
      // box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      transform: translateY(-1px);
    }

    &.selectedAgent {
      border-color: #1890ff;
      // box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    .agentContent {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 8px;

      .avatar {
        flex-shrink: 0;
      }

      .agentInfo {
        flex: 1;
        min-width: 0;

        .agentName {
          display: block;
          font-size: 13px;
          line-height: 1.3;
          margin-bottom: 2px;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .agentCategory {
          display: block;
          font-size: 11px;
          color: #8c8c8c;
          background: #f0f0f0;
          padding: 1px 4px;
          border-radius: 3px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .usageCount {
        margin-top: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .popularAgents {
    padding: 12px 16px;

    .agentCard {
      .agentContent {
        gap: 6px;

        .avatar {
          width: 32px !important;
          height: 32px !important;
          font-size: 14px;
        }

        .agentInfo {
          .agentName {
            font-size: 12px;
          }

          .agentCategory {
            font-size: 10px;
          }
        }
      }
    }
  }
}

// 深色主题支持
[data-theme="dark"] {
  .popularAgents {
    background: #1f1f1f;
    border-top-color: #303030;

    .header .title {
      color: #bfbfbf;
    }

    .agentCard {
      background: #262626;
      border-color: #404040;

      &:hover {
        border-color: #1890ff;
        background: #2a2a2a;
      }

      &.selectedAgent {
        border-color: #1890ff;
        background: #162312;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      .agentContent {
        .agentInfo {
          .agentName {
            color: #fff;
          }

          .agentCategory {
            background: #404040;
            color: #bfbfbf;
          }
        }
      }
    }
  }
}

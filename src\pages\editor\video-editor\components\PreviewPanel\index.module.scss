.previewPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
  
  .title {
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
  }

  .fileName {
    font-size: 14px;
    color: #64748b;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.videoContainer {
  flex: 1;
  background-color: #0f172a;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  .video {
    max-width: 100%;
    max-height: 100%;
  }

  .placeholder {
    color: #64748b;
    font-size: 14px;
  }
}

.controls {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;

  .timeDisplay {
    font-family: monospace;
    color: #64748b;
    font-size: 14px;
    margin: 0 16px;
    min-width: 80px;
    text-align: center;
  }
} 
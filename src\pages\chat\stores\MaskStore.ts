import { makeAutoObservable } from 'mobx';
import { nanoid } from 'nanoid';
import { Mask } from '../types';
import { StoreKey, BUILTIN_MASKS, BUILTIN_MASK_ID } from '../constants';

export class MaskStore {
  masks: Record<string, Mask> = {};

  constructor() {
    makeAutoObservable(this);
    this.loadFromLocalStorage();
  }

  get allMasks() {
    const userMasks = Object.values(this.masks).sort(
      (a, b) => b.createdAt - a.createdAt,
    );
    const builtin = BUILTIN_MASKS.map(
      (m) => ({ ...m, id: m.id.toString() } as Mask),
    );
    return userMasks.concat(builtin);
  }

  get(id?: string) {
    if (!id) return undefined;
    
    // 检查是否是内置面具
    if (id.startsWith(BUILTIN_MASK_ID.toString())) {
      return BUILTIN_MASKS.find(m => m.id.toString() === id);
    }
    
    return this.masks[id];
  }

  add(mask: Mask) {
    const newMask = { ...mask };
    if (!newMask.id) {
      newMask.id = nanoid();
    }
    newMask.createdAt = Date.now();
    newMask.builtin = false;
    
    this.masks[newMask.id] = newMask;
    this.saveToLocalStorage();
    
    return newMask;
  }

  remove(id: string) {
    // 不能删除内置面具
    if (id.startsWith(BUILTIN_MASK_ID.toString())) {
      return false;
    }
    
    delete this.masks[id];
    this.saveToLocalStorage();
    return true;
  }

  update(id: string, updater: (mask: Mask) => void) {
    // 不能修改内置面具
    if (id.startsWith(BUILTIN_MASK_ID.toString())) {
      return false;
    }
    
    const mask = this.masks[id];
    if (!mask) return false;
    
    updater(mask);
    this.saveToLocalStorage();
    return true;
  }

  search(text: string) {
    const searchText = text.toLowerCase();
    return this.allMasks.filter(
      (m) =>
        m.name.toLowerCase().includes(searchText) ||
        m.context.some((c) =>
          typeof c.content === 'string' 
            ? c.content.toLowerCase().includes(searchText)
            : false
        ),
    );
  }

  clone(id: string) {
    const mask = this.get(id);
    if (!mask) return null;
    
    const clonedMask: Mask = {
      ...mask,
      id: nanoid(),
      name: `${mask.name} (副本)`,
      createdAt: Date.now(),
      builtin: false,
    };
    
    this.masks[clonedMask.id] = clonedMask;
    this.saveToLocalStorage();
    
    return clonedMask;
  }

  getAll() {
    return this.allMasks;
  }

  getUserMasks() {
    return Object.values(this.masks).sort(
      (a, b) => b.createdAt - a.createdAt,
    );
  }

  getBuiltinMasks() {
    return BUILTIN_MASKS;
  }

  create(template?: Partial<Mask>): Mask {
    const newMask: Mask = {
      id: nanoid(),
      createdAt: Date.now(),
      avatar: "1f603",
      name: "新面具",
      hideContext: false,
      context: [],
      syncGlobalConfig: true,
      modelConfig: {
        model: "gpt-3.5-turbo",
        providerName: "OpenAI",
        temperature: 0.5,
        top_p: 1,
        max_tokens: 4000,
        presence_penalty: 0,
        frequency_penalty: 0,
        sendMemory: true,
        historyMessageCount: 4,
        compressMessageLengthThreshold: 1000,
        compressModel: "gpt-3.5-turbo",
        compressProviderName: "OpenAI",
        enableInjectSystemPrompts: true,
        template: "{{input}}",
        size: "1024x1024",
        quality: "standard",
        style: "vivid",
      },
      lang: "cn",
      builtin: false,
      ...template,
    };

    return newMask;
  }

  import(masks: Mask[]) {
    let importCount = 0;
    
    masks.forEach((mask) => {
      if (mask.id && !mask.builtin) {
        // 生成新的ID避免冲突
        const newMask = {
          ...mask,
          id: nanoid(),
          createdAt: Date.now(),
          builtin: false,
        };
        
        this.masks[newMask.id] = newMask;
        importCount++;
      }
    });
    
    if (importCount > 0) {
      this.saveToLocalStorage();
    }
    
    return importCount;
  }

  export() {
    return Object.values(this.masks);
  }

  saveToLocalStorage() {
    try {
      localStorage.setItem(StoreKey.Mask, JSON.stringify(this.masks));
    } catch (error) {
      console.error('Failed to save masks to localStorage:', error);
    }
  }

  loadFromLocalStorage() {
    try {
      const stored = localStorage.getItem(StoreKey.Mask);
      if (stored) {
        const data = JSON.parse(stored);
        this.masks = data || {};
      }
    } catch (error) {
      console.error('Failed to load masks from localStorage:', error);
      this.masks = {};
    }
  }

  clearAllData() {
    this.masks = {};
    localStorage.removeItem(StoreKey.Mask);
  }

  // 获取面具统计信息
  getStats() {
    const userMasks = this.getUserMasks();
    const builtinMasks = this.getBuiltinMasks();
    
    return {
      total: userMasks.length + builtinMasks.length,
      user: userMasks.length,
      builtin: builtinMasks.length,
    };
  }

  // 按标签过滤面具
  filterByTags(tags: string[]) {
    if (tags.length === 0) return this.allMasks;
    
    return this.allMasks.filter((mask) =>
      tags.some((tag) =>
        mask.name.toLowerCase().includes(tag.toLowerCase()) ||
        mask.context.some((c) =>
          typeof c.content === 'string' 
            ? c.content.toLowerCase().includes(tag.toLowerCase())
            : false
        )
      )
    );
  }

  // 获取最近使用的面具
  getRecentMasks(limit: number = 5) {
    return this.getUserMasks().slice(0, limit);
  }
}

import React, { useState } from "react";
import {
  Card,
  Row,
  Col,
  Avatar,
  Typography,
  Button,
  Input,
  Tabs,
  message,
} from "antd";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { useNavigate } from "react-router-dom";
import { Agent } from "../types";
import {
  MOCK_AGENTS,
  AGENT_CATEGORIES,
  getAgentsByCategory,
  searchAgents,
} from "../data/agents";
import { useChatStoreOnly } from "../stores";
import styles from "./AgentSelector.module.scss";

const { Text, Title } = Typography;
const { Search } = Input;
const { TabPane } = Tabs;

interface AgentSelectorProps {
  onAgentSelect?: (agent: Agent) => void;
}

export const AgentSelector: React.FC<AgentSelectorProps> = observer(
  ({ onAgentSelect }) => {
    const chatStore = useChatStoreOnly();
    const navigate = useNavigate();
    const [selectedCategory, setSelectedCategory] = useState("全部");
    const [searchKeyword, setSearchKeyword] = useState("");

    // 获取要显示的智能体列表
    const getDisplayAgents = (): Agent[] => {
      if (searchKeyword.trim()) {
        return searchAgents(searchKeyword);
      }
      return getAgentsByCategory(selectedCategory);
    };

    // 处理智能体选择
    const handleAgentSelect = (agent: Agent) => {
      try {
        // 创建新的智能体会话
        const session = chatStore.newAgentSession(agent.id);

        // 导航到聊天界面
        navigate(`/chat?sessionId=${session.id}`);

        // 调用回调
        onAgentSelect?.(agent);

        message.success(`已选择智能体：${agent.name}`);
      } catch (error) {
        console.error("选择智能体失败:", error);
        message.error("选择智能体失败，请重试");
      }
    };

    const displayAgents = getDisplayAgents();

    return (
      <div className={styles.agentSelector}>
        <div className={styles.header}>
          <Title level={3} style={{ margin: 0 }}>
            选择智能体
          </Title>
          <Text type="secondary">
            选择一个智能体开始对话，每个智能体都有专业的领域知识
          </Text>
        </div>

        <div className={styles.searchSection}>
          <Search
            placeholder="搜索智能体..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onSearch={setSearchKeyword}
            allowClear
            size="large"
            prefix={<SearchOutlined />}
            style={{ marginBottom: 16 }}
          />
        </div>

        <Tabs
          activeKey={selectedCategory}
          onChange={setSelectedCategory}
          type="card"
          className={styles.categoryTabs}
        >
          {AGENT_CATEGORIES.map((category) => (
            <TabPane tab={category.label} key={category.key}>
              <Row gutter={[16, 16]} className={styles.agentGrid}>
                {displayAgents.map((agent) => (
                  <Col key={agent.id} xs={24} sm={12} md={8} lg={6}>
                    <Card
                      hoverable
                      className={styles.agentCard}
                      actions={[
                        <Button
                          key="select"
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAgentSelect(agent)}
                          size="small"
                        >
                          选择
                        </Button>,
                      ]}
                    >
                      <Card.Meta
                        avatar={
                          <Avatar
                            src={agent.avatar}
                            size={48}
                            style={{ backgroundColor: "#1890ff" }}
                          >
                            {agent.name.charAt(0)}
                          </Avatar>
                        }
                        title={
                          <div className={styles.agentTitle}>
                            <Text strong>{agent.name}</Text>
                            <Text type="secondary" className={styles.category}>
                              {agent.category}
                            </Text>
                          </div>
                        }
                        description={
                          <div className={styles.agentDescription}>
                            <Text type="secondary" ellipsis={{ rows: 2 }}>
                              {agent.description}
                            </Text>
                            <div className={styles.usageCount}>
                              <Text
                                type="secondary"
                                style={{ fontSize: "12px" }}
                              >
                                使用次数: {agent.usageCount}
                              </Text>
                            </div>
                          </div>
                        }
                      />
                    </Card>
                  </Col>
                ))}
              </Row>

              {displayAgents.length === 0 && (
                <div className={styles.emptyState}>
                  <Text type="secondary">
                    {searchKeyword
                      ? "没有找到匹配的智能体"
                      : "该分类下暂无智能体"}
                  </Text>
                </div>
              )}
            </TabPane>
          ))}
        </Tabs>
      </div>
    );
  }
);

export default AgentSelector;

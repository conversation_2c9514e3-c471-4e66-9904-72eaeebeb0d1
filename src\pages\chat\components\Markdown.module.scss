/* Markdown 组件样式 */

.markdownContent {
  line-height: 1.6;
  color: #333;
}

/* 行内代码样式 */
.inlineCode {
  background-color: rgba(0, 0, 0, 0.08);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: Monaco, Consolas, "Courier New", monospace;
  color: #d63384;
}

/* 代码块包装器 */
.codeBlockWrapper {
  margin: 8px 0;
}

/* 代码块头部 */
.codeBlockHeader {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 6px 12px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  color: #666;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-weight: bold;
}

/* 代码块预格式化文本 */
.codeBlockPre {
  margin: 0;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 12px;
  overflow: auto;
  font-family: Monaco, Consolas, "Courier New", monospace;
  line-height: 1.4;

  &.withHeader {
    border-radius: 0 0 6px 6px;
  }

  &.withoutHeader {
    border-radius: 6px;
  }
}

/* 表格容器 */
.tableWrapper {
  overflow: auto;
  margin: 16px 0;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
}

/* 表格头部单元格 */
.tableHeader {
  border: 1px solid #ddd;
  padding: 8px 12px;
  background-color: #f5f5f5;
  text-align: left;
  font-weight: bold;
}

/* 表格数据单元格 */
.tableCell {
  border: 1px solid #ddd;
  padding: 8px 12px;
}

/* 链接样式 */
.link {
  color: #1890ff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

/* 引用块样式 */
.blockquote {
  border-left: 4px solid #ddd;
  margin: 16px 0;
  padding-left: 16px;
  color: #666;
  font-style: italic;
}

/* 列表样式 */
.list {
  padding-left: 20px;
  margin: 8px 0;
}

/* 段落样式 */
.paragraph {
  margin: 4px 0;
  line-height: 1.5;
}

/* 标题样式 */
.heading1 {
  font-weight: bold;
  margin: 20px 0 16px 0;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.heading2 {
  font-weight: bold;
  margin: 18px 0 14px 0;
}

.heading3 {
  font-weight: bold;
  margin: 16px 0 12px 0;
}

/* 加载状态样式 */
.loadingContainer {
  text-align: center;
  padding: 20px;
}

.loadingText {
  margin-left: 8px;
  color: #666;
}

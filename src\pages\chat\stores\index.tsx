import React, { createContext, useContext } from "react";
import { ChatStore } from "./ChatStore";
import { ConfigStore } from "./ConfigStore";
import { MaskStore } from "./MaskStore";
import { PromptStore } from "./PromptStore";

class ChatRootStore {
  chatStore: ChatStore;
  configStore: ConfigStore;
  maskStore: MaskStore;
  promptStore: PromptStore;

  constructor() {
    this.configStore = new ConfigStore();
    this.chatStore = new ChatStore();
    this.maskStore = new MaskStore();
    this.promptStore = new PromptStore();
  }
}

const ChatStoreContext = createContext<ChatRootStore | null>(null);

export const ChatStoreProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const store = React.useMemo(() => new ChatRootStore(), []);

  return (
    <ChatStoreContext.Provider value={store}>
      {children}
    </ChatStoreContext.Provider>
  );
};

export const useChatStore = () => {
  const store = useContext(ChatStoreContext);
  if (!store) {
    throw new Error("useChatStore must be used within ChatStoreProvider");
  }
  return store;
};

export const useChatStoreOnly = () => useChatStore().chatStore;
export const useConfigStore = () => useChatStore().configStore;
export const useMaskStore = () => useChatStore().maskStore;
export const usePromptStore = () => useChatStore().promptStore;

export default new ChatRootStore();

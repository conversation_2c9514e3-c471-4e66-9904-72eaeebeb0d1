import React, { useState } from "react";
import {
  Card,
  List,
  Button,
  Typography,
  Space,
  Tag,
  Modal,
  Input,
  message,
} from "antd";
import {
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { RouteConfig } from "@/types/route";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface Template {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
}

// 模板页面组件
const TemplatesPage: React.FC = observer(() => {
  const [templates, setTemplates] = useState<Template[]>([
    {
      id: "1",
      title: "代码解释",
      content: "请帮我解释以下代码的功能和工作原理：\n\n[在这里粘贴代码]",
      category: "编程",
      tags: ["代码", "解释", "编程"],
    },
    {
      id: "2",
      title: "文档总结",
      content: "请帮我总结以下文档的主要内容和关键点：\n\n[在这里粘贴文档内容]",
      category: "文档",
      tags: ["总结", "文档", "分析"],
    },
    {
      id: "3",
      title: "翻译助手",
      content:
        "请将以下内容翻译成中文，保持原意和语境：\n\n[在这里输入要翻译的内容]",
      category: "翻译",
      tags: ["翻译", "语言", "中文"],
    },
    {
      id: "4",
      title: "创意写作",
      content:
        "请帮我写一篇关于[主题]的创意文章，要求：\n- 字数约500字\n- 语言生动有趣\n- 结构清晰",
      category: "写作",
      tags: ["写作", "创意", "文章"],
    },
  ]);

  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    category: "",
    tags: "",
  });

  const handleUseTemplate = (template: Template) => {
    navigator.clipboard.writeText(template.content);
    message.success("模板内容已复制到剪贴板");
  };

  const handleEditTemplate = (template: Template) => {
    setEditingTemplate(template);
    setFormData({
      title: template.title,
      content: template.content,
      category: template.category,
      tags: template.tags.join(", "),
    });
    setModalVisible(true);
  };

  const handleAddTemplate = () => {
    setEditingTemplate(null);
    setFormData({
      title: "",
      content: "",
      category: "",
      tags: "",
    });
    setModalVisible(true);
  };

  const handleSaveTemplate = () => {
    if (!formData.title || !formData.content) {
      message.error("请填写标题和内容");
      return;
    }

    const newTemplate: Template = {
      id: editingTemplate?.id || Date.now().toString(),
      title: formData.title,
      content: formData.content,
      category: formData.category || "其他",
      tags: formData.tags
        .split(",")
        .map((tag) => tag.trim())
        .filter(Boolean),
    };

    if (editingTemplate) {
      setTemplates((prev) =>
        prev.map((t) => (t.id === editingTemplate.id ? newTemplate : t))
      );
      message.success("模板已更新");
    } else {
      setTemplates((prev) => [...prev, newTemplate]);
      message.success("模板已添加");
    }

    setModalVisible(false);
  };

  const handleDeleteTemplate = (templateId: string) => {
    setTemplates((prev) => prev.filter((t) => t.id !== templateId));
    message.success("模板已删除");
  };

  return (
    <div style={{ padding: "24px", height: "100%", overflow: "auto" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Card>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <div>
              <Title level={3}>
                <FileTextOutlined style={{ marginRight: "8px" }} />
                提示词模板
              </Title>
              <Text type="secondary">使用预设模板快速开始对话</Text>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddTemplate}
            >
              添加模板
            </Button>
          </div>
        </Card>

        <List
          grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
          dataSource={templates}
          renderItem={(template) => (
            <List.Item>
              <Card
                title={template.title}
                extra={
                  <Space>
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => handleUseTemplate(template)}
                    />
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditTemplate(template)}
                    />
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteTemplate(template.id)}
                    />
                  </Space>
                }
                size="small"
              >
                <Space
                  direction="vertical"
                  size="small"
                  style={{ width: "100%" }}
                >
                  <Paragraph ellipsis={{ rows: 3 }}>
                    {template.content}
                  </Paragraph>
                  <div>
                    <Tag color="blue">{template.category}</Tag>
                    {template.tags.map((tag) => (
                      <Tag key={tag}>{tag}</Tag>
                    ))}
                  </div>
                </Space>
              </Card>
            </List.Item>
          )}
        />
      </Space>

      <Modal
        title={editingTemplate ? "编辑模板" : "添加模板"}
        open={modalVisible}
        onOk={handleSaveTemplate}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Space direction="vertical" size="middle" style={{ width: "100%" }}>
          <Input
            placeholder="模板标题"
            value={formData.title}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, title: e.target.value }))
            }
          />
          <Input
            placeholder="分类"
            value={formData.category}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, category: e.target.value }))
            }
          />
          <Input
            placeholder="标签（用逗号分隔）"
            value={formData.tags}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, tags: e.target.value }))
            }
          />
          <TextArea
            placeholder="模板内容"
            rows={6}
            value={formData.content}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, content: e.target.value }))
            }
          />
        </Space>
      </Modal>
    </div>
  );
});

export default TemplatesPage;

export const routeConfig: RouteConfig = {
  title: "提示模板",
  icon: <FileTextOutlined />,
  layout: false,
  sort: 4,
};

.propertyPanel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #1e293b;
    }
  }

  .content {
    flex: 1;
    overflow: auto;
    padding: 16px;

    :global {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }

  .emptyState {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    font-size: 14px;
  }
} 
// App.module.scss
// .menuWrap {
//     background-color: #fff !important;

//     :global {
//         .ant-menu-item {
//             height: 100%;
//         }
//     }
// }

// .container {
//     padding: 20px;
//     background-color: #f7f9fc;
//     // min-height: 100vh;
//     box-sizing: border-box;
// }

.tabBar {
    display: flex;
    margin-bottom: 24px;
    background-color: #fff;

    .tabItem {

        display: flex;

        flex-direction: column;
        align-items: center;
        padding: 10px 20px;
        cursor: pointer;

        background-color: #fff;

        transition: all 0.2s ease-in-out;
        font-weight: 500;

        &:hover {
            background-color: #f3f1f1;
        }
    }

    .active {
        background-color: #1890ff;
        color: #fff;
        border-color: #1890ff #1890ff #fff;
    }

}

.hallCard {
    :global .ant-card-body {
        padding: 12px !important;
    }

    .cardHeader {
        display: flex;
        align-items: center;

        .agent_name {
            margin-left: 10px;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            line-height: 1.5;
        }
    }
}
import React, { useState, useRef, useCallback, useEffect } from "react";
import { Input, Button, Upload, message, Tooltip, Space } from "antd";
import { SendOutlined, PaperClipOutlined } from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import EmojiPicker from "emoji-picker-react";
import { useConfigStore } from "../stores";
import { useClickOutside } from "../utils/hooks";
import { compressImage, isImageFile } from "../utils/message";
import styles from "./MessageInput.module.scss";

const { TextArea } = Input;

interface MessageInputProps {
  onSend: (content: string, attachImages?: string[]) => void;
  disabled?: boolean;
  placeholder?: string;
  hasMessages?: boolean; // 是否有消息，用于控制布局
  needsAgentSelection?: boolean; // 是否需要选择智能体
}

export const MessageInput: React.FC<MessageInputProps> = observer(
  ({
    onSend,
    disabled = false,
    placeholder = "输入消息...",
    hasMessages = false,
    needsAgentSelection = false,
  }) => {
    const configStore = useConfigStore();

    const [input, setInput] = useState("");
    const [attachedImages, setAttachedImages] = useState<string[]>([]);
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [uploading, setUploading] = useState(false);

    const textareaRef = useRef<any>(null);
    const emojiPickerRef = useRef<HTMLDivElement>(null);

    const [isComposing, setIsComposing] = useState(false);

    useClickOutside(emojiPickerRef, () => setShowEmojiPicker(false));

    const handleSend = useCallback(() => {
      if (!input.trim() && attachedImages.length === 0) return;
      if (disabled) return;

      onSend(
        input.trim(),
        attachedImages.length > 0 ? attachedImages : undefined
      );
      setInput("");
      setAttachedImages([]);
    }, [input, attachedImages, onSend, disabled]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (
          e.key === "Enter" &&
          !isComposing &&
          !e.shiftKey &&
          !e.ctrlKey &&
          !e.altKey &&
          !e.metaKey
        ) {
          e.preventDefault();
          handleSend();
        }
      },
      [handleSend, isComposing]
    );

    const onCompositionStart = useCallback(() => {
      setIsComposing(true);
    }, []);

    const onCompositionEnd = useCallback(() => {
      setIsComposing(false);
    }, []);

    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(e.target.value);
      },
      []
    );

    const handleEmojiClick = useCallback((emojiData: any) => {
      setInput((prev) => prev + emojiData.emoji);
      setShowEmojiPicker(false);
      textareaRef.current?.focus();
    }, []);

    const handleImageUpload = useCallback(async (file: File) => {
      if (!isImageFile(file)) {
        message.error("只支持图片文件");
        return false;
      }

      if (file.size > 10 * 1024 * 1024) {
        // 10MB限制
        message.error("图片大小不能超过10MB");
        return false;
      }

      setUploading(true);

      try {
        const compressedImage = await compressImage(file, 1024);
        setAttachedImages((prev) => [...prev, compressedImage]);
        message.success("图片上传成功");
      } catch (error) {
        console.error("图片压缩失败:", error);
        message.error("图片处理失败");
      } finally {
        setUploading(false);
      }

      return false; // 阻止默认上传行为
    }, []);

    const removeImage = useCallback((index: number) => {
      setAttachedImages((prev) => prev.filter((_, i) => i !== index));
    }, []);

    // 自动聚焦
    useEffect(() => {
      if (textareaRef.current && !disabled) {
        textareaRef.current.focus();
      }
    }, [disabled]);

    return (
      <div className={styles.messageInputContainer}>
        {/* 附加的图片预览 */}
        {attachedImages.length > 0 && (
          <div className={styles.attachedImagesContainer}>
            <Space wrap>
              {attachedImages.map((image, index) => (
                <div key={index} className={styles.imagePreview}>
                  <img
                    src={image}
                    alt={`附件 ${index + 1}`}
                    className={styles.imagePreviewImg}
                  />
                  <Button
                    type="text"
                    size="small"
                    danger
                    onClick={() => removeImage(index)}
                    className={styles.imageRemoveButton}
                  >
                    ×
                  </Button>
                </div>
              ))}
            </Space>
          </div>
        )}

        <div
          className={`${styles.messageInputContainer} ${
            hasMessages ? styles.hasMessages : styles.noMessages
          }`}
        >
          <div className={styles.inputWrapper}>
            {/* 输入框 */}
            <div className={styles.textareaWrapper}>
              <TextArea
                ref={textareaRef}
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onCompositionStart={onCompositionStart}
                onCompositionEnd={onCompositionEnd}
                placeholder={
                  needsAgentSelection ? "请先选择智能体..." : placeholder
                }
                disabled={disabled || needsAgentSelection}
                autoSize={{ minRows: 3, maxRows: 6 }}
                className={styles.textarea}
                style={{
                  fontSize: configStore.fontSize,
                  background: "#fbfbfb",
                }}
              />

              {/* Emoji选择器 */}
              {showEmojiPicker && (
                <div ref={emojiPickerRef} className={styles.emojiPicker}>
                  <EmojiPicker
                    onEmojiClick={handleEmojiClick}
                    width={300}
                    height={400}
                  />
                </div>
              )}
            </div>

            <div className={styles.messageInputTools}>
              {/* 附件上传按钮 */}
              <Upload
                beforeUpload={handleImageUpload}
                showUploadList={false}
                accept="*/*"
                disabled={disabled || uploading}
              >
                <Tooltip title="上传附件">
                  <Button
                    icon={<PaperClipOutlined />}
                    loading={uploading}
                    disabled={disabled}
                    className={styles.attachmentButton}
                  >
                    {/* <Button icon={<SearchOutlined />}>Search</Button> */}
                    附件
                  </Button>
                </Tooltip>
              </Upload>

              {/* 发送按钮 */}
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSend}
                disabled={
                  disabled ||
                  needsAgentSelection ||
                  (!input.trim() && attachedImages.length === 0)
                }
                className={styles.sendButton}
              />
            </div>
          </div>
        </div>

        {/* 提示信息 */}
        {/* <div
          style={{
            marginTop: "8px",
            fontSize: "12px",
            color: "#999",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span>
            {configStore.submitKey === "Enter"
              ? "Enter发送，Shift+Enter换行"
              : "Ctrl+Enter发送"}
          </span>
          {input.length > 0 && <span>{input.length} 字符</span>}
        </div> */}
      </div>
    );
  }
);

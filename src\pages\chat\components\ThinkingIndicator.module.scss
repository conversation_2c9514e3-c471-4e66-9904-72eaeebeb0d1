/* ThinkingIndicator 组件样式 */

.thinkingContainer {
  display: flex;
  gap: 8px;
  padding: 4px 8px;
  align-items: center;
}

.contentWrapper {
  display: flex;
  align-items: center;
  flex: 1;
}

.indicatorContent {
  color: #333;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
  width: fit-content;
}

.dotsContainer {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #7b4ffe;
  animation: thinking 1.4s infinite ease-in-out;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }

  &:nth-child(3) {
    animation-delay: 0s;
  }
}

.textContent {
  color: #999;
  padding: 0 4px;
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

import React, { useState, useCallback, useEffect } from "react";
import { Layout, Typography, Button, Space, Dropdown, message } from "antd";
import {
  SettingOutlined,
  DeleteOutlined,
  ExportOutlined,
  MoreOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import {
  Outlet,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import { MessageList } from "./MessageList";
import { MessageInput } from "./MessageInput";
import { Settings } from "./Settings";
import { PopularAgents } from "./PopularAgents";
import { useChatStoreOnly, useConfigStore } from "../stores";
import { downloadAs } from "../utils/message";
import { defaultChatClient } from "../api/client";
import styles from "./ChatWindow.module.scss";

const { Header, Content } = Layout;
const { Title } = Typography;

interface ChatWindowProps {
  onOpenSettings?: () => void;
}

export const ChatWindow: React.FC<ChatWindowProps> = observer(
  ({ onOpenSettings }) => {
    const chatStore = useChatStoreOnly();
    const configStore = useConfigStore();
    const [settingsVisible, setSettingsVisible] = useState(false);
    const location = useLocation();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    const currentSession = chatStore.currentSession;
    // 使用会话级别的loading状态
    const sending = currentSession.isLoading || false;

    // 检查是否在子路由页面
    const isSubRoute = location.pathname.startsWith("/chat/");

    // 处理URL中的sessionId参数
    useEffect(() => {
      const sessionId = searchParams.get("sessionId");
      if (sessionId) {
        // 根据sessionId查找对应的session索引
        const sessionIndex = chatStore.sessions.findIndex(
          (session) => session.id === sessionId
        );
        if (
          sessionIndex >= 0 &&
          sessionIndex !== chatStore.currentSessionIndex
        ) {
          chatStore.selectSession(sessionIndex);
        }
      }
    }, [searchParams, chatStore]);

    const handleSendMessage = useCallback(
      async (content: string, attachImages?: string[]) => {
        if (sending) return;

        // 设置当前会话的加载状态
        chatStore.setSessionLoading(currentSession.id, true);

        try {
          // 记录发送前的消息数量，用于判断是否是第一条消息
          const messageCountBefore = currentSession.messages.length;
          const sessionId = currentSession.id;

          console.log("发送消息前的状态:", {
            sessionId,
            messageCountBefore,
            currentSessionIndex: chatStore.currentSessionIndex,
            sessionTopic: currentSession.topic,
          });

          // 打印详细的会话状态
          chatStore.debugSessionState();

          // 检查会话是否有正在进行的操作
          if (chatStore.hasPendingOperations(sessionId)) {
            message.warning("当前会话正在处理中，请稍后再试");
            return;
          }

          // 使用安全的异步操作包装器
          let result;
          try {
            result = await chatStore.withSessionLock(sessionId, async () => {
              console.log("在 withSessionLock 内部开始操作...");

              // 使用基于sessionId的安全方法添加用户消息，跳过锁检查因为已经在withSessionLock中
              const userInputSuccess = chatStore.onUserInputBySessionId(
                sessionId,
                content,
                attachImages,
                true // skipLockCheck = true
              );
              console.log("用户输入结果:", userInputSuccess);

              if (!userInputSuccess) {
                throw new Error("Failed to add user input to session");
              }

              // 如果这是第一条消息，更新URL包含sessionId
              if (messageCountBefore === 0) {
                navigate(`/chat?sessionId=${sessionId}`, { replace: true });
              }

              // 获取对话历史（使用当前会话快照）
              const sessionSnapshot = chatStore.sessions.find(
                (s) => s.id === sessionId
              );
              if (!sessionSnapshot) {
                throw new Error("Session not found after adding user input");
              }

              const messages = chatStore.getMessagesWithMemory(sessionSnapshot);

              // 使用基于sessionId的安全方法创建AI回复消息，跳过锁检查因为已经在withSessionLock中
              const botMessage = chatStore.onBotMessageBySessionId(
                sessionId,
                "",
                true, // streaming = true
                true // skipLockCheck = true
              );
              console.log("机器人消息创建结果:", !!botMessage);

              if (!botMessage) {
                throw new Error("Failed to create bot message for session");
              }

              console.log("withSessionLock 操作完成");
              return { messages, botMessage, sessionId, sessionSnapshot };
            });
          } catch (lockError) {
            console.error("withSessionLock 操作失败:", lockError);
            const errorMessage =
              lockError instanceof Error
                ? lockError.message
                : String(lockError);
            message.error("操作失败: " + errorMessage);
            return;
          }

          if (!result) {
            message.error("无法获取会话锁，请重试");
            return;
          }

          const { messages } = result;

          try {
            // 调用AI API进行流式回复
            let fullResponse = "";
            let isFirstChunk = true;

            for await (const chunk of defaultChatClient.streamChat(messages)) {
              fullResponse = chunk;

              // 使用基于sessionId的安全更新方法，使用会话快照的消息长度
              const currentSessionSnapshot = chatStore.sessions.find(
                (s) => s.id === sessionId
              );
              if (currentSessionSnapshot) {
                chatStore.updateMessageBySessionId(
                  sessionId,
                  currentSessionSnapshot.messages.length - 1,
                  (message) => {
                    if (message) {
                      message.content = fullResponse;
                      message.streaming = true;

                      // 第一个chunk时，添加一个小延迟来显示思考过程
                      if (isFirstChunk) {
                        isFirstChunk = false;
                      }
                    }
                  }
                );
              }

              // 添加小延迟，让用户能看到流式效果
              await new Promise((resolve) => setTimeout(resolve, 50));
            }

            // 完成流式回复
            const finalSessionSnapshot = chatStore.sessions.find(
              (s) => s.id === sessionId
            );
            if (finalSessionSnapshot) {
              chatStore.updateMessageBySessionId(
                sessionId,
                finalSessionSnapshot.messages.length - 1,
                (message) => {
                  if (message) {
                    message.streaming = false;
                  }
                }
              );

              // 如果启用了自动生成标题且这是第一次对话
              if (
                configStore.enableAutoGenerateTitle &&
                finalSessionSnapshot.messages.length === 2 &&
                finalSessionSnapshot.topic === "新的聊天"
              ) {
                try {
                  const title = await defaultChatClient.generateTitle(messages);
                  chatStore.updateMessageBySessionId(sessionId, -1, () => {
                    if (finalSessionSnapshot) {
                      finalSessionSnapshot.topic = title;
                    }
                  });
                } catch (error) {
                  console.warn("生成标题失败:", error);
                }
              }
            }
          } catch (apiError) {
            console.error("AI回复失败:", apiError);

            // 标记消息为错误状态
            const errorSessionSnapshot = chatStore.sessions.find(
              (s) => s.id === sessionId
            );
            if (errorSessionSnapshot) {
              chatStore.updateMessageBySessionId(
                sessionId,
                errorSessionSnapshot.messages.length - 1,
                (message) => {
                  if (message) {
                    message.content =
                      "抱歉，我现在无法回复您的消息。请稍后重试。";
                    message.isError = true;
                    message.streaming = false;
                  }
                }
              );
            }

            message.error("AI回复失败，请重试");
          }
        } catch (error) {
          console.error("发送消息失败:", error);
          message.error("发送消息失败，请重试");
        } finally {
          // 清除当前会话的加载状态
          chatStore.setSessionLoading(currentSession.id, false);
        }
      },
      [chatStore, configStore, currentSession, sending]
    );

    const handleDeleteMessage = useCallback(
      (messageId: string) => {
        const sessionIndex = chatStore.currentSessionIndex;
        const messageIndex = currentSession.messages.findIndex(
          (m) => m.id === messageId
        );

        if (messageIndex >= 0) {
          chatStore.updateMessage(sessionIndex, messageIndex, () => {
            currentSession.messages.splice(messageIndex, 1);
          });
          message.success("消息已删除");
        }
      },
      [chatStore, currentSession]
    );

    const handleClearSession = useCallback(() => {
      chatStore.resetSession();
      message.success("会话已清空");
    }, [chatStore]);

    const handleExportSession = useCallback(() => {
      const content = currentSession.messages
        .map((msg) => {
          const role = msg.role === "user" ? "用户" : "AI助手";
          const textContent =
            typeof msg.content === "string"
              ? msg.content
              : msg.content
                  .filter((c) => c.type === "text")
                  .map((c) => c.text)
                  .join("");
          return `${role} (${msg.date}):\n${textContent}\n`;
        })
        .join("\n---\n\n");

      const filename = `聊天记录_${
        currentSession.topic
      }_${new Date().toLocaleDateString()}.txt`;
      downloadAs(content, filename);
      message.success("聊天记录已导出");
    }, [currentSession]);

    const headerMenuItems = [
      {
        key: "export",
        label: "导出聊天记录",
        icon: <ExportOutlined />,
        onClick: handleExportSession,
      },
      {
        key: "clear",
        label: "清空会话",
        icon: <DeleteOutlined />,
        onClick: handleClearSession,
        danger: true,
      },
      // {
      //   key: "settings",
      //   label: "设置",
      //   icon: <SettingOutlined />,
      //   onClick: () => setSettingsVisible(true),
      // },
    ];

    return (
      <>
        <Layout
          style={{
            height: "100%",
            backgroundColor: "#fbfbfb",
            paddingBottom: "20px",
          }}
        >
          {/* 聊天头部 */}
          <Header
            style={{
              backgroundColor: "#fff",
              borderBottom: "1px solid #f0f0f0",
              padding: "0 16px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              height: "64px",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <Title
                level={4}
                style={{
                  margin: 0,
                  fontSize: configStore.fontSize * 1.2,
                  color: "#333",
                }}
              >
                {currentSession.topic}
              </Title>

              {currentSession.messages.length > 0 && (
                <span
                  style={{
                    fontSize: configStore.fontSize * 0.8,
                    color: "#999",
                  }}
                >
                  {currentSession.messages.length} 条消息
                </span>
              )}
            </div>

            <Space>
              <Dropdown
                menu={{ items: headerMenuItems }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <Button
                  type="text"
                  icon={<MoreOutlined />}
                  style={{ fontSize: "16px" }}
                />
              </Dropdown>
            </Space>
          </Header>

          {/* 消息列表或子路由内容 */}
          <Content className={styles.content}>
            {isSubRoute ? (
              // 显示子路由内容
              <Outlet />
            ) : currentSession.messages.length > 0 ? (
              // 有消息时的正常布局
              <>
                <MessageList
                  messages={currentSession.messages}
                  onDeleteMessage={handleDeleteMessage}
                  loading={sending}
                  sessionId={currentSession.id}
                />

                {/* 消息输入框 */}
                <MessageInput
                  onSend={handleSendMessage}
                  disabled={sending}
                  placeholder={sending ? "AI正在思考中..." : "输入消息..."}
                  hasMessages={true}
                  needsAgentSelection={false}
                />
              </>
            ) : (
              // 没有消息时的居中布局
              <div className={styles.centeredContent}>
                <MessageList
                  messages={currentSession.messages}
                  onDeleteMessage={handleDeleteMessage}
                  loading={sending}
                  sessionId={currentSession.id}
                />

                {/* 消息输入框 */}
                <MessageInput
                  onSend={handleSendMessage}
                  disabled={sending}
                  placeholder={sending ? "AI正在思考中..." : "输入消息..."}
                  hasMessages={false}
                  needsAgentSelection={!currentSession.agentId}
                />

                {/* 热门智能体选择 - 仅在没有消息时显示 */}
                {currentSession.messages.length === 0 && (
                  <PopularAgents sessionId={currentSession.id} />
                )}
              </div>
            )}
          </Content>
        </Layout>

        {/* 设置弹窗 */}
        <Settings
          visible={settingsVisible}
          onClose={() => setSettingsVisible(false)}
        />
      </>
    );
  }
);

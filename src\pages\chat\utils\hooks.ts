import { useEffect, useState, useRef, useCallback } from 'react';
import { useDebounce } from 'use-debounce';

export function useMobileScreen() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
}

export function useScrollToBottom() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const scrollToBottom = useCallback(() => {
    const dom = scrollRef.current;
    if (dom) {
      requestAnimationFrame(() => {
        setAutoScroll(true);
        dom.scrollTo(0, dom.scrollHeight);
      });
    }
  }, []);

  const scrollDomToBottom = useCallback(() => {
    const dom = scrollRef.current;
    if (dom) {
      requestAnimationFrame(() => {
        dom.scrollTo(0, dom.scrollHeight);
      });
    }
  }, []);

  // check if should auto scroll
  const onChatBodyScroll = useCallback((e: HTMLElement) => {
    const bottomHeight = e.scrollTop + e.clientHeight;
    const edgeThreshold = e.clientHeight / 4;
    const isAtBottom = bottomHeight >= e.scrollHeight - edgeThreshold;
    setAutoScroll(isAtBottom);
  }, []);

  return {
    scrollRef,
    autoScroll,
    setAutoScroll,
    scrollToBottom,
    scrollDomToBottom,
    onChatBodyScroll,
  };
}

export function useSubmitHandler() {
  const isComposing = useRef(false);

  const shouldSubmit = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key !== "Enter") return false;
    if (e.key === "Enter" && (e.nativeEvent.isComposing || isComposing.current)) {
      isComposing.current = false;
      return false;
    }
    return (
      (e.ctrlKey && e.key === "Enter") ||
      (e.shiftKey && e.key === "Enter") ||
      (e.altKey && e.key === "Enter") ||
      (e.metaKey && e.key === "Enter") ||
      (!e.ctrlKey && !e.shiftKey && !e.altKey && !e.metaKey)
    );
  }, []);

  const onCompositionStart = useCallback(() => {
    isComposing.current = true;
  }, []);

  const onCompositionEnd = useCallback(() => {
    isComposing.current = false;
  }, []);

  return {
    shouldSubmit,
    onCompositionStart,
    onCompositionEnd,
  };
}

export function usePromptHints() {
  const [hints, setHints] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const updateHints = useCallback((newHints: string[]) => {
    setHints(newHints);
    setSelectedIndex(0);
  }, []);

  const selectNext = useCallback(() => {
    setSelectedIndex((prev) => (prev + 1) % hints.length);
  }, [hints.length]);

  const selectPrev = useCallback(() => {
    setSelectedIndex((prev) => (prev - 1 + hints.length) % hints.length);
  }, [hints.length]);

  const getSelectedHint = useCallback(() => {
    return hints[selectedIndex] || "";
  }, [hints, selectedIndex]);

  return {
    hints,
    selectedIndex,
    updateHints,
    selectNext,
    selectPrev,
    getSelectedHint,
  };
}

export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const [debouncedCallback] = useDebounce(callback, delay);
  return debouncedCallback as T;
}

export function useLocalStorage<T>(
  key: string,
  defaultValue: T
): [T, (value: T) => void] {
  const [value, setValue] = useState<T>(() => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  const setStoredValue = useCallback((newValue: T) => {
    try {
      setValue(newValue);
      localStorage.setItem(key, JSON.stringify(newValue));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key]);

  return [value, setStoredValue];
}

export function useClickOutside(
  ref: React.RefObject<HTMLElement>,
  handler: () => void
) {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler]);
}

export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCall = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const throttledCallback = useCallback((...args: any[]) => {
    const now = Date.now();
    
    if (now - lastCall.current >= delay) {
      lastCall.current = now;
      return callback(...args);
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        lastCall.current = Date.now();
        callback(...args);
      }, delay - (now - lastCall.current));
    }
  }, [callback, delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return throttledCallback as T;
}

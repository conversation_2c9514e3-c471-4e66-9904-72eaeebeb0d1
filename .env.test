# 测试环境配置
VITE_NODE_ENV=test

# 项目基础配置
VITE_APP_TITLE=React Admin Test
VITE_APP_DESC=React Admin 测试环境

# 服务配置
VITE_PORT=5173
VITE_PROXY=true

# API配置
VITE_API_URL=http://test-api.example.com
VITE_API_PREFIX=/api
VITE_UPLOAD_URL=http://test-api.example.com/upload
VITE_WS_URL=ws://test-api.example.com/ws

# 功能配置
VITE_USE_MOCK=false
VITE_USE_PWA=false
VITE_USE_HTTPS=false

# 调试配置
VITE_DEV_TOOLS=true
VITE_DROP_CONSOLE=true
VITE_DROP_DEBUGGER=true

# 测试特有配置
VITE_TEST_ACCOUNT=admin
VITE_TEST_PASSWORD=123456 
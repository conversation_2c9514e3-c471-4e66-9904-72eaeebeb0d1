.materialPanel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .title {
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
  }

  .uploadButton {
    width: 32px;
    height: 32px;
    border: 1px dashed #e2e8f0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      color: #3b82f6;
      border-color: #3b82f6;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  padding: 16px;
}

.materialItem {
  .thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    cursor: grab;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .duration {
      position: absolute;
      bottom: 4px;
      right: 4px;
      padding: 2px 4px;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      font-size: 10px;
      border-radius: 2px;
    }

    .playIcon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 24px;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover {
      .playIcon {
        opacity: 1;
      }
    }

    &:active {
      cursor: grabbing;
    }
  }

  .fileName {
    margin-top: 4px;
    font-size: 12px;
    color: #475569;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.previewVideo {
  display: none;
} 
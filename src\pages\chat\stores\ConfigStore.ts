import { makeAutoObservable } from 'mobx';
import { StoreKey, DEFAULT_CONFIG, Theme, SubmitKey } from '../constants';
import { ChatConfig, ModelConfig, TTSConfig } from '../types';

export class ConfigStore {
  config: ChatConfig = { ...DEFAULT_CONFIG };

  constructor() {
    makeAutoObservable(this);
    this.loadFromLocalStorage();
  }

  reset() {
    this.config = { ...DEFAULT_CONFIG };
    this.saveToLocalStorage();
  }

  getConfig() {
    return this.config;
  }

  updateConfig(updater: (config: ChatConfig) => void) {
    updater(this.config);
    this.saveToLocalStorage();
  }

  // 主题相关
  get theme() {
    return this.config.theme;
  }

  setTheme(theme: Theme) {
    this.config.theme = theme;
    this.saveToLocalStorage();
  }

  // 字体大小
  get fontSize() {
    return this.config.fontSize;
  }

  setFontSize(fontSize: number) {
    this.config.fontSize = Math.max(12, Math.min(24, fontSize));
    this.saveToLocalStorage();
  }

  // 侧边栏宽度
  get sidebarWidth() {
    return this.config.sidebarWidth;
  }

  setSidebarWidth(width: number) {
    this.config.sidebarWidth = Math.max(200, Math.min(500, width));
    this.saveToLocalStorage();
  }

  // 提交键
  get submitKey() {
    return this.config.submitKey;
  }

  setSubmitKey(key: SubmitKey) {
    this.config.submitKey = key;
    this.saveToLocalStorage();
  }

  // 头像
  get avatar() {
    return this.config.avatar;
  }

  setAvatar(avatar: string) {
    this.config.avatar = avatar;
    this.saveToLocalStorage();
  }

  // 模型配置
  get modelConfig() {
    return this.config.modelConfig;
  }

  updateModelConfig(updater: (config: ModelConfig) => void) {
    updater(this.config.modelConfig);
    this.saveToLocalStorage();
  }

  // TTS配置
  get ttsConfig() {
    return this.config.ttsConfig;
  }

  updateTTSConfig(updater: (config: TTSConfig) => void) {
    updater(this.config.ttsConfig);
    this.saveToLocalStorage();
  }

  // 自动生成标题
  get enableAutoGenerateTitle() {
    return this.config.enableAutoGenerateTitle;
  }

  setEnableAutoGenerateTitle(enable: boolean) {
    this.config.enableAutoGenerateTitle = enable;
    this.saveToLocalStorage();
  }

  // 发送预览气泡
  get sendPreviewBubble() {
    return this.config.sendPreviewBubble;
  }

  setSendPreviewBubble(enable: boolean) {
    this.config.sendPreviewBubble = enable;
    this.saveToLocalStorage();
  }

  // 紧凑边框
  get tightBorder() {
    return this.config.tightBorder;
  }

  setTightBorder(enable: boolean) {
    this.config.tightBorder = enable;
    this.saveToLocalStorage();
  }

  // 禁用提示词提示
  get disablePromptHint() {
    return this.config.disablePromptHint;
  }

  setDisablePromptHint(disable: boolean) {
    this.config.disablePromptHint = disable;
    this.saveToLocalStorage();
  }

  // 不显示面具启动屏
  get dontShowMaskSplashScreen() {
    return this.config.dontShowMaskSplashScreen;
  }

  setDontShowMaskSplashScreen(dont: boolean) {
    this.config.dontShowMaskSplashScreen = dont;
    this.saveToLocalStorage();
  }

  // 隐藏内置面具
  get hideBuiltinMasks() {
    return this.config.hideBuiltinMasks;
  }

  setHideBuiltinMasks(hide: boolean) {
    this.config.hideBuiltinMasks = hide;
    this.saveToLocalStorage();
  }

  // 自定义模型
  get customModels() {
    return this.config.customModels;
  }

  setCustomModels(models: string) {
    this.config.customModels = models;
    this.saveToLocalStorage();
  }

  // 默认模型
  get defaultModel() {
    return this.config.defaultModel;
  }

  setDefaultModel(model: string) {
    this.config.defaultModel = model;
    this.saveToLocalStorage();
  }

  // 历史消息数量
  get historyMessageCount() {
    return this.config.historyMessageCount;
  }

  setHistoryMessageCount(count: number) {
    this.config.historyMessageCount = Math.max(0, Math.min(64, count));
    this.saveToLocalStorage();
  }

  // 压缩消息长度阈值
  get compressMessageLengthThreshold() {
    return this.config.compressMessageLengthThreshold;
  }

  setCompressMessageLengthThreshold(threshold: number) {
    this.config.compressMessageLengthThreshold = Math.max(500, Math.min(4000, threshold));
    this.saveToLocalStorage();
  }

  // 发送机器人消息
  get sendBotMessages() {
    return this.config.sendBotMessages;
  }

  setSendBotMessages(send: boolean) {
    this.config.sendBotMessages = send;
    this.saveToLocalStorage();
  }

  saveToLocalStorage() {
    try {
      localStorage.setItem(StoreKey.Config, JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save config to localStorage:', error);
    }
  }

  loadFromLocalStorage() {
    try {
      const stored = localStorage.getItem(StoreKey.Config);
      if (stored) {
        const data = JSON.parse(stored);
        this.config = { ...DEFAULT_CONFIG, ...data };
      }
    } catch (error) {
      console.error('Failed to load config from localStorage:', error);
      this.config = { ...DEFAULT_CONFIG };
    }
  }

  clearAllData() {
    this.config = { ...DEFAULT_CONFIG };
    localStorage.removeItem(StoreKey.Config);
  }

  exportConfig() {
    return JSON.stringify(this.config, null, 2);
  }

  importConfig(configJson: string) {
    try {
      const config = JSON.parse(configJson);
      this.config = { ...DEFAULT_CONFIG, ...config };
      this.saveToLocalStorage();
      return true;
    } catch (error) {
      console.error('Failed to import config:', error);
      return false;
    }
  }
}

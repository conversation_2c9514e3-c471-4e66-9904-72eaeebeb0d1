/* 聊天界面主容器 */
.chat {
  display: flex;

  background: var(--bg-primary);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
  .sidebar {

    display: flex;
    // flex-direction: column;
  }
}

/* 聊天头部 */
.chatHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-primary);
  backdrop-filter: blur(8px);

  .headerTitle {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .headerActions {
    display: flex;
    gap: 8px;
  }
}

/* 聊天消息区域 */
.chatBody {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: var(--bg-secondary);
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 3px;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--text-tertiary);
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--border-secondary) transparent;
}

/* 消息容器 */
.messageContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 100%;
}

/* 单条消息 */
.message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 80%;
  animation: slideIn 0.3s ease-out;

  &.userMessage {
    align-self: flex-end;
    flex-direction: row-reverse;

    .messageContent {
      background: var(--primary-color);
      color: white;
      border-bottom-right-radius: 4px;
    }
  }

  &.assistantMessage {
    align-self: flex-start;

    .messageContent {
      background: var(--bg-primary);
      color: var(--text-primary);
      border: 1px solid var(--border-primary);
      border-bottom-left-radius: 4px;
    }
  }
}

/* 消息头像 */
.messageAvatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: 14px;
  flex-shrink: 0;
  border: 1px solid var(--border-primary);
}

/* 消息内容 */
.messageContent {
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  word-wrap: break-word;
  line-height: 1.6;
  font-size: 14px;
  box-shadow: var(--shadow-sm);
  position: relative;

  /* Markdown 样式 */
  :global {
    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    code {
      background: rgba(0, 0, 0, 0.1);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
    }

    pre {
      background: var(--bg-tertiary);
      padding: 12px;
      border-radius: var(--radius);
      overflow-x: auto;
      margin: 8px 0;

      code {
        background: none;
        padding: 0;
      }
    }

    blockquote {
      border-left: 3px solid var(--primary-color);
      padding-left: 12px;
      margin: 8px 0;
      color: var(--text-secondary);
    }
  }
}

/* 消息操作按钮 */
.messageActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-top: 4px;

  .message:hover & {
    opacity: 1;
  }
}

/* 聊天输入区域 */
.chatInput {
  padding: 16px 20px;
  border-top: 1px solid var(--border-primary);
  background: var(--bg-primary);
  backdrop-filter: blur(8px);
}

/* 输入框容器 */
.inputContainer {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 100%;

  .inputWrapper {
    flex: 1;
    position: relative;

    :global(.ant-input) {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.5;
      resize: none;
      min-height: 44px;
      max-height: 120px;

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.1);
      }

      &::placeholder {
        color: var(--text-disabled);
      }
    }
  }

  .sendButton {
    flex-shrink: 0;

    :global(.ant-btn) {
      height: 44px;
      padding: 0 20px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;

      &:not(:disabled) {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;

        &:hover {
          opacity: 0.9;
          transform: translateY(-1px);
        }
      }
    }
  }
}

/* 欢迎消息 */
.welcomeMessage {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);

  .welcomeTitle {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
  }

  .welcomeSubtitle {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .welcomeActions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* 加载状态 */
.loadingMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;

  .loadingDots {
    display: flex;
    gap: 2px;

    span {
      width: 4px;
      height: 4px;
      background: var(--text-tertiary);
      border-radius: 50%;
      animation: loadingDot 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

/* 动画定义 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message {
    max-width: 90%;
  }

  .chatBody {
    padding: 16px;
  }

  .chatInput {
    padding: 12px 16px;
  }

  .inputContainer {
    gap: 8px;

    .sendButton :global(.ant-btn) {
      padding: 0 16px;
    }
  }

  .welcomeMessage {
    padding: 40px 16px;

    .welcomeTitle {
      font-size: 20px;
    }

    .welcomeSubtitle {
      font-size: 14px;
    }
  }
}
$ballSize: 10px;
$containerSize: 100px;
$n: 24;
$pDeg: 360deg/$n;
$t:2s;
$delay:2s;

.container {
    width: 100%;
    height: 100%;
    background: transparent;
    opacity: 0.2;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 120px;
    min-height: 120px;

    // 定义主题变量
    --loading-primary: var(--ant-primary-color, #1890ff);
    --loading-secondary: var(--ant-primary-6, #1890ff);
    --loading-bg-light: rgba(0, 0, 0, 0.2);
    --loading-bg-dark: rgba(255, 255, 255, 0.85);

    .loading {
        width: $containerSize;
        height: $containerSize;
        margin: 0 auto;
        position: relative;
        border-radius: 50%;

        .dot {
            position: absolute;
            left: 50%;
            top: 0;
            width: $ballSize;
            height: $ballSize;

            margin-left: -($ballSize/2);
            margin-right: -($ballSize/2);
            perspective: 70px;
            transform-style: preserve-3d;

            transform-origin: center $containerSize / 2 + $ballSize/2;

            @for $i from 1 through $n {
                &:nth-child(#{$i}) {
                    transform: rotate($pDeg * ( $i - 1 ));
                    &::after, &::before {
                        animation-delay: -$delay /  $n * ( $i - 1 ) *6;
                    }
                }
                    // animation: rotate#{$i} 1s linear infinite;
                }
         
        &::before,
        &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;

            border-radius: 50%;
        }

        &::before {
            background: var(--loading-bg-light);
            opacity: 0.8;
            top: -100%;
            animation: rotate-white $t linear infinite;
            @keyframes rotate-white {
                0% {
                    animation-timing-function: ease-in;
                }
                25% {
                    transform: translate3d(0,100%,$ballSize);
                    animation-timing-function: ease-out;
                }
                50% { 
                    transform: translate3d(0,200%,0);
                    animation-timing-function: ease-in;
                }
                75% {
                    transform: translate3d(0,100%,-$ballSize);
                    animation-timing-function: ease-out;
                }
                // 100% {
                //     transform: rotate(360deg);
                // }
            }
        }

        &::after {
            background: var(--loading-primary);
            opacity: 0.9;
            top: 100%;
            animation: rotate-black $t linear infinite;
            @keyframes rotate-black {
                0% {
                    animation-timing-function: ease-in;
                }
                25% {
                    transform: translate3d(0,-100%,-$ballSize);
                    animation-timing-function: ease-out;
                }
                50% { 
                    transform: translate3d(0,-200%,0);
                    animation-timing-function: ease-in;
                }
                75% {
                    transform: translate3d(0,-100%,$ballSize);
                    animation-timing-function: ease-out;
                }
                // 100% {
                //     transform: rotate(360deg);
                // }
            }
        }
        }

    }
}

// 暗色主题
[data-theme='dark'] .container {
    --loading-bg-light: rgba(255, 255, 255, 0.25);
    --loading-primary: var(--ant-primary-color, #177ddc);
    --loading-secondary: var(--ant-primary-6, #177ddc);
}
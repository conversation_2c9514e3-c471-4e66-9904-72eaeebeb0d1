import React, { useMemo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import remarkBreaks from "remark-breaks";
import rehypeKatex from "rehype-katex";
import rehypeHighlight from "rehype-highlight";
import { Spin } from "antd";
import "katex/dist/katex.min.css";
import "highlight.js/styles/default.css";
import styles from "./Markdown.module.scss";

interface MarkdownProps {
  content: string;
  loading?: boolean;
  fontSize?: number;
  fontFamily?: string;
  className?: string;
}

export const Markdown: React.FC<MarkdownProps> = ({
  content,
  loading = false,
  fontSize = 14,
  fontFamily = "inherit",
  className = "",
}) => {
  const markdownContent = useMemo(() => {
    if (loading && !content) {
      return "正在思考中...";
    }
    return content || "";
  }, [content, loading]);

  const components = useMemo(
    () => ({
      // 自定义代码块渲染
      code: ({ node, inline, className, children, ...props }: any) => {
        const match = /language-(\w+)/.exec(className || "");
        const language = match ? match[1] : "";

        if (inline) {
          return (
            <code
              className={`${styles.inlineCode} ${className || ""}`}
              style={{
                fontSize: fontSize * 0.9,
              }}
              {...props}
            >
              {children}
            </code>
          );
        }

        return (
          <div className={styles.codeBlockWrapper}>
            {language && (
              <div
                className={styles.codeBlockHeader}
                style={{
                  fontSize: fontSize * 0.75,
                }}
              >
                {language}
              </div>
            )}
            <pre
              className={`${styles.codeBlockPre} ${
                language ? styles.withHeader : styles.withoutHeader
              }`}
              style={{
                fontSize: fontSize * 0.85,
              }}
            >
              <code className={className} {...props}>
                {children}
              </code>
            </pre>
          </div>
        );
      },

      // 自定义表格渲染
      table: ({ children, ...props }: any) => (
        <div className={styles.tableWrapper}>
          <table
            className={styles.table}
            style={{
              fontSize: fontSize,
              fontFamily,
            }}
            {...props}
          >
            {children}
          </table>
        </div>
      ),

      th: ({ children, ...props }: any) => (
        <th className={styles.tableHeader} {...props}>
          {children}
        </th>
      ),

      td: ({ children, ...props }: any) => (
        <td className={styles.tableCell} {...props}>
          {children}
        </td>
      ),

      // 自定义链接渲染
      a: ({ children, href, ...props }: any) => (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className={styles.link}
          {...props}
        >
          {children}
        </a>
      ),

      // 自定义引用块渲染
      blockquote: ({ children, ...props }: any) => (
        <blockquote className={styles.blockquote} {...props}>
          {children}
        </blockquote>
      ),

      // 自定义列表渲染
      ul: ({ children, ...props }: any) => (
        <ul className={styles.list} {...props}>
          {children}
        </ul>
      ),

      ol: ({ children, ...props }: any) => (
        <ol className={styles.list} {...props}>
          {children}
        </ol>
      ),

      // 自定义段落渲染
      p: ({ children, ...props }: any) => (
        <p
          className={styles.paragraph}
          style={{
            fontSize,
            fontFamily,
          }}
          {...props}
        >
          {children}
        </p>
      ),

      // 自定义标题渲染
      h1: ({ children, ...props }: any) => (
        <h1
          className={styles.heading1}
          style={{
            fontSize: fontSize * 1.8,
          }}
          {...props}
        >
          {children}
        </h1>
      ),

      h2: ({ children, ...props }: any) => (
        <h2
          className={styles.heading2}
          style={{
            fontSize: fontSize * 1.5,
          }}
          {...props}
        >
          {children}
        </h2>
      ),

      h3: ({ children, ...props }: any) => (
        <h3
          className={styles.heading3}
          style={{
            fontSize: fontSize * 1.3,
          }}
          {...props}
        >
          {children}
        </h3>
      ),
    }),
    [fontSize, fontFamily]
  );

  if (loading && !content) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="small" />
        <span className={styles.loadingText} style={{ fontSize }}>
          正在生成回复...
        </span>
      </div>
    );
  }

  return (
    <div
      className={`${styles.markdownContent} markdown-content ${className}`}
      style={{
        fontSize,
        fontFamily,
      }}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath, remarkBreaks]}
        rehypePlugins={[rehypeKatex, rehypeHighlight]}
        components={components}
      >
        {markdownContent}
      </ReactMarkdown>
    </div>
  );
};

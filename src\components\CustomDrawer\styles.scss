// 遮罩层动画
.drawer-enter .drawer-mask {
  opacity: 0;
}
.drawer-enter-active .drawer-mask {
  opacity: 1;
  transition: opacity 150ms ease-out;
}
.drawer-exit .drawer-mask {
  opacity: 1;
}
.drawer-exit-active .drawer-mask {
  opacity: 0;
  transition: opacity 150ms ease-out;
}

// 抽屉内容动画
.drawer-content-left {
  left: 0;
  &.drawer-enter {
    transform: translateX(-100%);
  }
  &.drawer-enter-active {
    transform: translateX(0);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  &.drawer-exit {
    transform: translateX(0);
  }
  &.drawer-exit-active {
    transform: translateX(-100%);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.drawer-content-right {
  right: 0;
  &.drawer-enter {
    transform: translateX(100%);
  }
  &.drawer-enter-active {
    transform: translateX(0);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  &.drawer-exit {
    transform: translateX(0);
  }
  &.drawer-exit-active {
    transform: translateX(100%);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// Mac 风格特殊效果
.drawer-mac {
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    opacity: 0;
  }

  &:hover::before {
    opacity: 1;
  }
}

// Mac 风格滚动条
.mac-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  // 亮色模式下的滚动条
  .theme-container:not(.dark) & {
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      
      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }
} 
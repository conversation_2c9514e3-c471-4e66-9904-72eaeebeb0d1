import React from "react";
import { Card, Typography, Space, Avatar, Tag } from "antd";
import {
  MessageOutlined,
  UserOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { RouteConfig } from "@/types/route";
import { useChatStoreOnly } from "../stores";

const { Title, Text, Paragraph } = Typography;

// 对话页面组件
const ConversationPage: React.FC = observer(() => {
  const chatStore = useChatStoreOnly();
  const currentSession = chatStore.currentSession;

  return (
    <div style={{ padding: "24px", height: "100%", overflow: "auto" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Card>
          <Title level={3}>
            <MessageOutlined style={{ marginRight: "8px" }} />
            当前对话
          </Title>
          <Paragraph>这里显示当前对话的详细信息和统计数据。</Paragraph>
        </Card>

        <Card title="对话统计">
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Text strong>对话标题:</Text>
              <Text>{currentSession.topic}</Text>
            </div>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Text strong>消息数量:</Text>
              <Tag color="blue">{currentSession.messages.length} 条</Tag>
            </div>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Text strong>创建时间:</Text>
              <Text>
                {new Date(currentSession.lastUpdate).toLocaleString()}
              </Text>
            </div>
          </Space>
        </Card>

        <Card title="参与者">
          <Space direction="vertical" size="middle">
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <Avatar icon={<UserOutlined />} />
              <div>
                <Text strong>用户</Text>
                <br />
                <Text type="secondary">对话发起者</Text>
              </div>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <Avatar
                icon={<RobotOutlined />}
                style={{ backgroundColor: "#52c41a" }}
              />
              <div>
                <Text strong>AI助手</Text>
                <br />
                <Text type="secondary">智能对话助手</Text>
              </div>
            </div>
          </Space>
        </Card>
      </Space>
    </div>
  );
});

export default ConversationPage;

export const routeConfig: RouteConfig = {
  title: "当前对话",
  icon: <MessageOutlined />,
  layout: false,
  sort: 1,
};

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Vite & TypeScript
vite.config.js
vite.config.d.ts
vite.config.js.map
*.tsbuildinfo
tsconfig.tsbuildinfo

# 构建产物
dist.tar.gz
stats.html

# 依赖
node_modules

# 环境变量
.env.local
.env.*.local

# 编辑器
.vscode/*
!.vscode/extensions.json
.idea

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 系统文件
.DS_Store
Thumbs.db
